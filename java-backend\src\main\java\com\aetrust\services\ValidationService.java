package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;
import java.util.List;
import java.util.Arrays;

@Slf4j
@Service
public class ValidationService {
    
    // Email validation pattern (RFC 5322 compliant)
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    // Phone validation pattern (international format)
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^\\+[1-9]\\d{1,14}$"
    );
    
    // OTP code pattern (6 digits)
    private static final Pattern OTP_PATTERN = Pattern.compile(
        "^\\d{6}$"
    );
    
    // Password strength pattern (at least 8 chars, 1 upper, 1 lower, 1 digit, 1 special)
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"
    );
    
    // PIN pattern (4-6 digits)
    private static final Pattern PIN_PATTERN = Pattern.compile(
        "^\\d{4,6}$"
    );
    
    // Name pattern (letters, spaces, hyphens, apostrophes)
    private static final Pattern NAME_PATTERN = Pattern.compile(
        "^[a-zA-Z\\s\\-']{2,50}$"
    );
    
    // Username pattern (alphanumeric, underscore, hyphen, 3-30 chars)
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_-]{3,30}$"
    );
    
    // Common weak passwords
    private static final List<String> WEAK_PASSWORDS = Arrays.asList(
        "password", "123456", "123456789", "qwerty", "abc123", "password123",
        "admin", "letmein", "welcome", "monkey", "1234567890"
    );
    
    /**
     * Validate email format
     */
    public boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        email = email.trim().toLowerCase();
        
        // Check length
        if (email.length() > 254) {
            return false;
        }
        
        // Check pattern
        return EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * Validate phone number format
     */
    public boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        
        phone = phone.trim();
        
        // Check pattern
        return PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * Validate OTP code format
     */
    public boolean isValidOtpCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        code = code.trim();
        
        // Check pattern
        return OTP_PATTERN.matcher(code).matches();
    }
    
    /**
     * Validate password strength
     */
    public boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        
        // Check length
        if (password.length() < 8 || password.length() > 128) {
            return false;
        }
        
        // Check for weak passwords
        if (WEAK_PASSWORDS.contains(password.toLowerCase())) {
            return false;
        }
        
        // Check pattern
        return PASSWORD_PATTERN.matcher(password).matches();
    }
    
    /**
     * Validate PIN format
     */
    public boolean isValidPin(String pin) {
        if (pin == null || pin.trim().isEmpty()) {
            return false;
        }
        
        pin = pin.trim();
        
        // Check pattern
        if (!PIN_PATTERN.matcher(pin).matches()) {
            return false;
        }
        
        // Check for sequential numbers
        if (isSequentialNumbers(pin)) {
            return false;
        }
        
        // Check for repeated numbers
        if (isRepeatedNumbers(pin)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate name format
     */
    public boolean isValidName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        name = name.trim();
        
        // Check pattern
        return NAME_PATTERN.matcher(name).matches();
    }
    
    /**
     * Validate username format
     */
    public boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        username = username.trim().toLowerCase();
        
        // Check pattern
        return USERNAME_PATTERN.matcher(username).matches();
    }
    
    /**
     * Validate date of birth (must be at least 18 years old)
     */
    public boolean isValidDateOfBirth(java.time.LocalDate dateOfBirth) {
        if (dateOfBirth == null) {
            return false;
        }
        
        java.time.LocalDate now = java.time.LocalDate.now();
        java.time.LocalDate eighteenYearsAgo = now.minusYears(18);
        
        // Must be at least 18 years old
        if (dateOfBirth.isAfter(eighteenYearsAgo)) {
            return false;
        }
        
        // Must not be more than 120 years old
        java.time.LocalDate oneHundredTwentyYearsAgo = now.minusYears(120);
        if (dateOfBirth.isBefore(oneHundredTwentyYearsAgo)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate registration ID format
     */
    public boolean isValidRegistrationId(String registrationId) {
        if (registrationId == null || registrationId.trim().isEmpty()) {
            return false;
        }
        
        registrationId = registrationId.trim();
        
        // Should be 32-64 character alphanumeric string
        return registrationId.matches("^[a-zA-Z0-9]{32,64}$");
    }
    
    /**
     * Validate UUID format
     */
    public boolean isValidUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return false;
        }
        
        try {
            java.util.UUID.fromString(uuid);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Sanitize input string to prevent XSS
     */
    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        return input.trim()
            .replaceAll("<", "&lt;")
            .replaceAll(">", "&gt;")
            .replaceAll("\"", "&quot;")
            .replaceAll("'", "&#x27;")
            .replaceAll("/", "&#x2F;");
    }
    
    /**
     * Check if PIN contains sequential numbers
     */
    private boolean isSequentialNumbers(String pin) {
        for (int i = 0; i < pin.length() - 1; i++) {
            int current = Character.getNumericValue(pin.charAt(i));
            int next = Character.getNumericValue(pin.charAt(i + 1));
            
            if (next != current + 1) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Check if PIN contains repeated numbers
     */
    private boolean isRepeatedNumbers(String pin) {
        char firstChar = pin.charAt(0);
        for (int i = 1; i < pin.length(); i++) {
            if (pin.charAt(i) != firstChar) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get password strength score (0-100)
     */
    public int getPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return 0;
        }
        
        int score = 0;
        
        // Length bonus
        if (password.length() >= 8) score += 25;
        if (password.length() >= 12) score += 25;
        
        // Character variety bonus
        if (password.matches(".*[a-z].*")) score += 10;
        if (password.matches(".*[A-Z].*")) score += 10;
        if (password.matches(".*\\d.*")) score += 10;
        if (password.matches(".*[@$!%*?&].*")) score += 10;
        
        // Complexity bonus
        if (password.matches(".*[a-z].*[A-Z].*\\d.*[@$!%*?&].*")) score += 10;
        
        return Math.min(score, 100);
    }
    
    /**
     * Validate business registration number format
     */
    public boolean isValidBusinessRegistrationNumber(String regNumber) {
        if (regNumber == null || regNumber.trim().isEmpty()) {
            return false;
        }
        
        regNumber = regNumber.trim();
        
        // Basic format validation (alphanumeric, 6-20 characters)
        return regNumber.matches("^[a-zA-Z0-9]{6,20}$");
    }
    
    /**
     * Validate tax identification number format
     */
    public boolean isValidTaxId(String taxId) {
        if (taxId == null || taxId.trim().isEmpty()) {
            return false;
        }
        
        taxId = taxId.trim();
        
        // Basic format validation (alphanumeric, 6-20 characters)
        return taxId.matches("^[a-zA-Z0-9]{6,20}$");
    }
}
