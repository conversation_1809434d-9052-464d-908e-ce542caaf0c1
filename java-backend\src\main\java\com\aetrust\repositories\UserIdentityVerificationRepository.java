package com.aetrust.repositories;

import com.aetrust.models.User;
import com.aetrust.types.Types.VerificationStatus;
import com.aetrust.types.Types.IdType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserIdentityVerificationRepository extends JpaRepository<User.UserIdentityVerification, Long> {

    Optional<User.UserIdentityVerification> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<User.UserIdentityVerification> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    List<User.UserIdentityVerification> findByVerificationStatusAndDeletedAtIsNull(VerificationStatus status);
    List<User.UserIdentityVerification> findByIdTypeAndDeletedAtIsNull(IdType idType);
    List<User.UserIdentityVerification> findByVerifierIdAndDeletedAtIsNull(Long verifierId);

    @Query("SELECT v FROM User$UserIdentityVerification v WHERE v.verificationStatus = :status AND v.createdAt >= :date AND v.deletedAt IS NULL")
    List<User.UserIdentityVerification> findByStatusAndCreatedAfter(@Param("status") VerificationStatus status, @Param("date") LocalDateTime date);

    @Query("SELECT COUNT(v) FROM User$UserIdentityVerification v WHERE v.verificationStatus = :status AND v.deletedAt IS NULL")
    long countByVerificationStatus(@Param("status") VerificationStatus status);

    @Query("SELECT v.idType, COUNT(v) FROM User$UserIdentityVerification v WHERE v.deletedAt IS NULL GROUP BY v.idType")
    List<Object[]> getIdTypeDistribution();

    @Query("SELECT v FROM User$UserIdentityVerification v WHERE v.verificationStatus = 'PENDING' AND v.createdAt <= :cutoffDate AND v.deletedAt IS NULL")
    List<User.UserIdentityVerification> findPendingVerificationsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    @Modifying
    @Query("UPDATE User$UserIdentityVerification v SET v.deletedAt = :deletedAt WHERE v.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserIdentityVerification v SET v.deletedAt = :deletedAt WHERE v.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
}
