package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class InMemorySessionService {
    
    private final ConcurrentHashMap<String, SessionData> sessions = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    public InMemorySessionService() {
        // Clean up expired sessions every 5 minutes
        scheduler.scheduleAtFixedRate(this::cleanupExpiredSessions, 5, 5, TimeUnit.MINUTES);
        log.info("InMemorySessionService initialized - Redis replacement active");
    }
    
  
    public void set(String key, String value, long timeout, TimeUnit unit) {
        long expirationTime = System.currentTimeMillis() + unit.toMillis(timeout);
        sessions.put(key, new SessionData(value, expirationTime));
        log.debug("Session stored: {} (expires in {} {})", key, timeout, unit);
    }
    
  
    public String get(String key) {
        SessionData data = sessions.get(key);
        if (data == null) {
            return null;
        }
        
        if (data.isExpired()) {
            sessions.remove(key);
            log.debug("Session expired and removed: {}", key);
            return null;
        }
        
        return data.getValue();
    }
    
  
    public void delete(String key) {
        sessions.remove(key);
        log.debug("Session deleted: {}", key);
    }
    
    
    public boolean exists(String key) {
        SessionData data = sessions.get(key);
        if (data == null) {
            return false;
        }
        
        if (data.isExpired()) {
            sessions.remove(key);
            return false;
        }
        
        return true;
    }
    
  
    public java.util.Set<String> keys(String pattern) {
        return sessions.keySet();
    }
    
    
    private void cleanupExpiredSessions() {
        long now = System.currentTimeMillis();
        final int[] removed = {0};

        sessions.entrySet().removeIf(entry -> {
            if (entry.getValue().getExpirationTime() < now) {
                removed[0]++;
                return true;
            }
            return false;
        });

        if (removed[0] > 0) {
            log.debug("Cleaned up {} expired sessions", removed[0]);
        }
    }
    

    public int getSessionCount() {
        return sessions.size();
    }

  
    public int size() {
        cleanupExpiredSessions();
        return sessions.size();
    }

   
    public void clear() {
        sessions.clear();
        log.info("All in-memory sessions cleared");
    }
    

    private static class SessionData {
        private final String value;
        private final long expirationTime;
        
        public SessionData(String value, long expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }
        
        public String getValue() {
            return value;
        }
        
        public long getExpirationTime() {
            return expirationTime;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }
    }
}
