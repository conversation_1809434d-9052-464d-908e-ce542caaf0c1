package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class TransactionService {

    private final PostgresService postgresService;
    private final DatabaseService databaseService;
    
    @Autowired
    public TransactionService(PostgresService postgresService, DatabaseService databaseService) {
        this.postgresService = postgresService;
        this.databaseService = databaseService;
    }
    
    // get all transactions for a user
    public List<Map<String, Object>> getUserTransactions(String userId) throws SQLException {
        try {
            return postgresService.getFilteredRecords("transactions", "user_id", userId);
        } catch (SQLException e) {
            log.error("Failed to get transactions for user: {}", userId, e);
            throw e;
        }
    }

    // get transactions by status
    public List<Map<String, Object>> getTransactionsByStatus(String status) throws SQLException {
        try {
            return postgresService.getFilteredRecords("transactions", "status", status);
        } catch (SQLException e) {
            log.error("Failed to get transactions by status: {}", status, e);
            throw e;
        }
    }
    
    // create new transaction
    @Transactional
    public String createTransaction(String userId, String type, BigDecimal amount, String description) throws SQLException {
        try {
            String transactionId = UUID.randomUUID().toString();
            
            Map<String, Object> transactionData = new HashMap<>();
            transactionData.put("id", transactionId);
            transactionData.put("user_id", userId);
            transactionData.put("type", type);
            transactionData.put("amount", amount);
            transactionData.put("description", description);
            transactionData.put("status", "pending");
            transactionData.put("created_at", Timestamp.valueOf(LocalDateTime.now()));
            transactionData.put("updated_at", Timestamp.valueOf(LocalDateTime.now()));
            
            int inserted = postgresService.insertRecord("transactions", transactionData);
            
            if (inserted > 0) {
                log.info("Transaction created successfully: {}", transactionId);
                return transactionId;
            } else {
                throw new SQLException("Failed to insert transaction record");
            }
            
        } catch (SQLException e) {
            log.error("Failed to create transaction for user: {}", userId, e);
            throw e;
        }
    }
    
    // update transaction status
    @Transactional
    public boolean updateTransactionStatus(String transactionId, String newStatus) throws SQLException {
        try {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("status", newStatus);
            updateData.put("updated_at", Timestamp.valueOf(LocalDateTime.now()));
            
            int updated = postgresService.updateRecord("transactions", updateData, "id", transactionId);
            
            if (updated > 0) {
                log.info("Transaction status updated: {} -> {}", transactionId, newStatus);
                return true;
            } else {
                log.warn("No transaction found with id: {}", transactionId);
                return false;
            }
            
        } catch (SQLException e) {
            log.error("Failed to update transaction status: {}", transactionId, e);
            throw e;
        }
    }
    
    // get transaction summary for user
    public Map<String, Object> getUserTransactionSummary(String userId) throws SQLException {
        try {
            String sql = """
                SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN type = 'credit' THEN amount ELSE 0 END) as total_credits,
                    SUM(CASE WHEN type = 'debit' THEN amount ELSE 0 END) as total_debits,
                    SUM(CASE WHEN type = 'credit' THEN amount ELSE -amount END) as net_amount,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transactions,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions
                FROM transactions 
                WHERE user_id = ?
                """;
            
            var results = postgresService.executeCustomQuery(sql, userId);
            
            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                // return empty summary if no transactions found
                Map<String, Object> emptySummary = new HashMap<>();
                emptySummary.put("total_transactions", 0);
                emptySummary.put("total_credits", BigDecimal.ZERO);
                emptySummary.put("total_debits", BigDecimal.ZERO);
                emptySummary.put("net_amount", BigDecimal.ZERO);
                emptySummary.put("completed_transactions", 0);
                emptySummary.put("pending_transactions", 0);
                emptySummary.put("failed_transactions", 0);
                return emptySummary;
            }
            
        } catch (SQLException e) {
            log.error("Failed to get transaction summary for user: {}", userId, e);
            throw e;
        }
    }
    
    // get recent transactions with pagination
    public List<Map<String, Object>> getRecentTransactions(int limit, int offset) throws SQLException {
        try {
            String sql = """
                SELECT id, user_id, type, amount, description, status, created_at, updated_at
                FROM transactions 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
                """;
            
            return postgresService.executeCustomQuery(sql, limit, offset);
            
        } catch (SQLException e) {
            log.error("Failed to get recent transactions", e);
            throw e;
        }
    }
    
    // get transactions by date range
    public List<Map<String, Object>> getTransactionsByDateRange(String userId, LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        try {
            String sql = """
                SELECT id, user_id, type, amount, description, status, created_at, updated_at
                FROM transactions 
                WHERE user_id = ? AND created_at BETWEEN ? AND ?
                ORDER BY created_at DESC
                """;
            
            return postgresService.executeCustomQuery(sql, userId,
                    Timestamp.valueOf(startDate), Timestamp.valueOf(endDate));
            
        } catch (SQLException e) {
            log.error("Failed to get transactions by date range for user: {}", userId, e);
            throw e;
        }
    }
    
    // process bulk transaction updates
    @Transactional
    public int processBulkStatusUpdate(List<String> transactionIds, String newStatus) throws SQLException {
        try {
            int totalUpdated = 0;
            
            for (String transactionId : transactionIds) {
                Map<String, Object> updateData = new HashMap<>();
                updateData.put("status", newStatus);
                updateData.put("updated_at", Timestamp.valueOf(LocalDateTime.now()));
                
                int updated = postgresService.updateRecord("transactions", updateData, "id", transactionId);
                totalUpdated += updated;
            }
            
            log.info("Bulk status update completed: {} transactions updated to {}", totalUpdated, newStatus);
            return totalUpdated;
            
        } catch (SQLException e) {
            log.error("Failed to process bulk status update", e);
            throw e;
        }
    }
    
    // get transaction statistics
    public Map<String, Object> getTransactionStatistics() throws SQLException {
        try {
            String sql = """
                SELECT 
                    COUNT(*) as total_transactions,
                    COUNT(DISTINCT user_id) as unique_users,
                    SUM(amount) as total_volume,
                    AVG(amount) as average_amount,
                    MIN(amount) as min_amount,
                    MAX(amount) as max_amount,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                    COUNT(CASE WHEN type = 'credit' THEN 1 END) as credit_count,
                    COUNT(CASE WHEN type = 'debit' THEN 1 END) as debit_count
                FROM transactions
                """;
            
            var results = postgresService.executeCustomQuery(sql);

            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                return new HashMap<>();
            }
            
        } catch (SQLException e) {
            log.error("Failed to get transaction statistics", e);
            throw e;
        }
    }
    
    // check if transaction exists
    public boolean transactionExists(String transactionId) throws SQLException {
        try {
            String sql = "SELECT COUNT(*) as count FROM transactions WHERE id = ?";
            var results = postgresService.executeCustomQuery(sql, transactionId);
            
            if (!results.isEmpty()) {
                Object countObj = results.get(0).get("count");
                if (countObj instanceof Number) {
                    return ((Number) countObj).intValue() > 0;
                }
            }
            return false;
            
        } catch (SQLException e) {
            log.error("Failed to check transaction existence: {}", transactionId, e);
            throw e;
        }
    }
    
    // delete old transactions
    @Transactional
    public int deleteOldTransactions(int daysOld) throws SQLException {
        try {
            String sql = "DELETE FROM transactions WHERE created_at < NOW() - INTERVAL ? DAY";
            int deleted = postgresService.executeCustomUpdate(sql, daysOld);
            
            log.info("Deleted {} old transactions (older than {} days)", deleted, daysOld);
            return deleted;
            
        } catch (SQLException e) {
            log.error("Failed to delete old transactions", e);
            throw e;
        }
    }
    
    // get daily transaction volume
    public List<Map<String, Object>> getDailyTransactionVolume(int days) throws SQLException {
        try {
            String sql = """
                SELECT 
                    DATE(created_at) as transaction_date,
                    COUNT(*) as transaction_count,
                    SUM(amount) as total_volume,
                    AVG(amount) as average_amount
                FROM transactions 
                WHERE created_at >= NOW() - INTERVAL ? DAY
                GROUP BY DATE(created_at)
                ORDER BY transaction_date DESC
                """;
            
            return postgresService.executeCustomQuery(sql, days);
            
        } catch (SQLException e) {
            log.error("Failed to get daily transaction volume", e);
            throw e;
        }
    }
}
