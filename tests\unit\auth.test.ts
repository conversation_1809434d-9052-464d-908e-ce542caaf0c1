import { AuthService } from '../../src/services/auth.service';
import { User } from '../../src/models/user.model';
import { createTestUser, setupTestDb, teardownTestDb, clearDatabase } from '../setup';
import bcrypt from 'bcrypt';

describe('AuthService', () => {
  beforeAll(async () => {
    await setupTestDb();
  });

  afterAll(async () => {
    await teardownTestDb();
  });

  beforeEach(async () => {
    await clearDatabase();
  });

  describe('login', () => {
    it('should login user with valid credentials', async () => {
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('Test123!@#', 10)
      });

      const user = new User(userData);
      await user.save();

      const result = await AuthService.login({
        email: '<EMAIL>',
        password: 'Test123!@#'
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('token');
      expect(result.data).toHaveProperty('user');
      if (result.success && result.data) {
        expect(result.data.user.email).toBe('<EMAIL>');
      }

      // Cleanup
      await User.findByIdAndDelete(user._id);
    });

    it('should fail login with invalid email', async () => {
      const result = await AuthService.login({
        email: '<EMAIL>',
        password: 'Test123!@#'
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('invalid credentials');
    });

    it('should fail login with invalid password', async () => {
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('Test123!@#', 10)
      });
      
      const user = new User(userData);
      await user.save();

      const result = await AuthService.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('invalid credentials');
    });

    it('should fail login for locked account', async () => {
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('Test123!@#', 10),
        security: {
          login_attempts: 5,
          locked_until: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
          two_factor_enabled: false
        }
      });

      const user = new User(userData);
      await user.save();

      const result = await AuthService.login({
        email: '<EMAIL>',
        password: 'Test123!@#'
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('account is locked');

      // Cleanup
      await User.findByIdAndDelete(user._id);
    });
  });

  describe('validateToken', () => {
    it('should validate valid JWT token', async () => {
      const userData = createTestUser({
        password: await bcrypt.hash('Test123!@#', 10)
      });
      const user = new User(userData);
      await user.save();

      const loginResult = await AuthService.login({
        email: userData.email,
        password: 'Test123!@#'
      });

      if (!loginResult.success || !loginResult.data) {
        throw new Error('Login failed in test setup');
      }

      const token = loginResult.data.token;
      const validationResult = await AuthService.validateToken(token);

      expect(validationResult.success).toBe(true);
      expect(validationResult.data).toHaveProperty('user');
    });

    it('should reject invalid JWT token', async () => {
      const result = await AuthService.validateToken('invalid-token');

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.message).toContain('invalid token');
      }
    });

    it('should reject expired JWT token', async () => {
      const result = await AuthService.validateToken('expired.token.here');

      expect(result.success).toBe(false);
    });
  });

  describe('refreshToken', () => {
    it('should refresh valid token', async () => {
      const userData = createTestUser({
        password: await bcrypt.hash('Test123!@#', 10)
      });
      const user = new User(userData);
      await user.save();

      const loginResult = await AuthService.login({
        email: userData.email,
        password: 'Test123!@#'
      });

      if (!loginResult.success || !loginResult.data) {
        throw new Error('Login failed in test setup');
      }

      const refreshResult = await AuthService.refreshToken(loginResult.data.token);

      expect(refreshResult.success).toBe(true);
      if (refreshResult.success && refreshResult.data && 'token' in refreshResult.data) {
        expect(refreshResult.data).toHaveProperty('token');
        expect(refreshResult.data.token).not.toBe(loginResult.data.token);
      }
    });

    it('should fail to refresh invalid token', async () => {
      const result = await AuthService.refreshToken('invalid-token');

      expect(result.success).toBe(false);
      if (!result.success && 'message' in result) {
        expect(result.message).toContain('invalid token');
      }
    });
  });

  describe('logout', () => {
    it('should logout user successfully', async () => {
      const userData = createTestUser({
        password: await bcrypt.hash('Test123!@#', 10)
      });
      const user = new User(userData);
      await user.save();

      const loginResult = await AuthService.login({
        email: userData.email,
        password: 'Test123!@#'
      });

      if (!loginResult.success || !loginResult.data) {
        throw new Error('Login failed in test setup');
      }

      const logoutResult = await AuthService.logout(loginResult.data.token);

      expect(logoutResult.success).toBe(true);
      expect(logoutResult.message).toContain('logged out successfully');
    });
  });

  describe('changePassword', () => {
    it('should change password with valid current password', async () => {
      const userData = createTestUser({
        password: await bcrypt.hash('OldPassword123!', 10)
      });
      const user = new User(userData);
      await user.save();

      const result = await AuthService.changePassword(user._id.toString(), {
        currentPassword: 'OldPassword123!',
        newPassword: 'NewPassword123!',
        confirmPassword: 'NewPassword123!'
      });

      expect(result.success).toBe(true);
      expect(result.message).toContain('password changed successfully');
    });

    it('should fail to change password with invalid current password', async () => {
      const userData = createTestUser({
        password: await bcrypt.hash('OldPassword123!', 10)
      });
      const user = new User(userData);
      await user.save();

      const result = await AuthService.changePassword(user._id.toString(), {
        currentPassword: 'WrongPassword',
        newPassword: 'NewPassword123!',
        confirmPassword: 'NewPassword123!'
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('current password is incorrect');
    });

    it('should fail when new passwords do not match', async () => {
      const userData = createTestUser({
        password: await bcrypt.hash('OldPassword123!', 10)
      });
      const user = new User(userData);
      await user.save();

      const result = await AuthService.changePassword(user._id.toString(), {
        currentPassword: 'OldPassword123!',
        newPassword: 'NewPassword123!',
        confirmPassword: 'DifferentPassword123!'
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('passwords do not match');
    });
  });

  describe('resetPassword', () => {
    it('should initiate password reset for valid email', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const result = await AuthService.initiatePasswordReset(userData.email);

      expect(result.success).toBe(true);
      expect(result.message).toContain('password reset email sent');
    });

    it('should fail password reset for non-existent email', async () => {
      const result = await AuthService.initiatePasswordReset('<EMAIL>');

      expect(result.success).toBe(false);
      expect(result.message).toContain('user not found');
    });
  });
});
