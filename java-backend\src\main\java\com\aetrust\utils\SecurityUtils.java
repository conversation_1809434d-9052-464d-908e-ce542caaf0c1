package com.aetrust.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.owasp.encoder.Encode;

import java.util.regex.Pattern;
import java.util.Map;
import java.util.HashMap;

/**
 * OWASP Security Utilities for Input Validation and Output Encoding
 * Addresses OWASP A03:2021 - Injection vulnerabilities
 */
@Component
public class SecurityUtils {
    
    @Value("${aetrust.feature-flags.input-validation.enabled:true}")
    private boolean inputValidationEnabled;
    
    @Value("${aetrust.feature-flags.output-encoding.enabled:true}")
    private boolean outputEncodingEnabled;

    @Autowired
    private CryptoUtils cryptoUtils;
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^\\+?[1-9]\\d{1,14}$");
    
    private static final Pattern ALPHANUMERIC_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_-]+$");
    
    private static final Pattern SAFE_TEXT_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9\\s.,!?'-]+$");
    
    // SQL Injection patterns
    private static final Pattern[] SQL_INJECTION_PATTERNS = {
        Pattern.compile("(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute).*"),
        Pattern.compile("(?i).*(\\||;|--|#|/\\*|\\*/).*"),
        Pattern.compile("(?i).*(char|nchar|varchar|nvarchar|ascii|substring).*\\(.*\\).*"),
        Pattern.compile("(?i).*(waitfor|delay|sleep|benchmark|pg_sleep|dbms_pipe).*")
    };
    
    // XSS patterns
    private static final Pattern[] XSS_PATTERNS = {
        Pattern.compile("(?i).*<script[^>]*>.*</script>.*"),
        Pattern.compile("(?i).*javascript:.*"),
        Pattern.compile("(?i).*on\\w+\\s*=.*"),
        Pattern.compile("(?i).*<(iframe|object|embed|link|meta|form)[^>]*>.*")
    };
    
    /**
     * OWASP A03:2021 - Injection Prevention
     * Validate input against injection attacks
     */
    public ValidationResult validateInput(String input, InputType type) {
        if (!inputValidationEnabled) {
            return ValidationResult.valid();
        }
        
        if (input == null || input.trim().isEmpty()) {
            return ValidationResult.invalid("Input cannot be empty");
        }
        
        // Check for injection patterns
        if (containsSqlInjection(input)) {
            return ValidationResult.invalid("SQL injection attempt detected");
        }
        
        if (containsXss(input)) {
            return ValidationResult.invalid("XSS attempt detected");
        }
        
        // Type-specific validation
        switch (type) {
            case EMAIL:
                return EMAIL_PATTERN.matcher(input).matches() ? 
                    ValidationResult.valid() : ValidationResult.invalid("Invalid email format");
            
            case PHONE:
                return PHONE_PATTERN.matcher(input).matches() ? 
                    ValidationResult.valid() : ValidationResult.invalid("Invalid phone format");
            
            case ALPHANUMERIC:
                return ALPHANUMERIC_PATTERN.matcher(input).matches() ? 
                    ValidationResult.valid() : ValidationResult.invalid("Only alphanumeric characters allowed");
            
            case SAFE_TEXT:
                return SAFE_TEXT_PATTERN.matcher(input).matches() ? 
                    ValidationResult.valid() : ValidationResult.invalid("Contains unsafe characters");
            
            case FREE_TEXT:
                return input.length() <= 1000 ? 
                    ValidationResult.valid() : ValidationResult.invalid("Text too long");
            
            default:
                return ValidationResult.valid();
        }
    }
    
    /**
     * OWASP A03:2021 - Output Encoding
     * Encode output to prevent XSS
     */
    public String encodeForHtml(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forHtml(input);
    }
    
    public String encodeForJavaScript(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forJavaScript(input);
    }
    
    public String encodeForUrl(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forUriComponent(input);
    }
    
    public String encodeForCss(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forCssString(input);
    }
    
    /**
     * Sanitize input by removing dangerous characters
     */
    public String sanitizeInput(String input) {
        if (input == null) return null;
        
        return input
            .replaceAll("<script[^>]*>.*?</script>", "")
            .replaceAll("<[^>]+>", "")
            .replaceAll("javascript:", "")
            .replaceAll("on\\w+\\s*=", "")
            .replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "")
            .trim();
    }
    
    /**
     * Generate secure random tokens
     */
    public String generateSecureToken(int length) {
        return cryptoUtils.generateSecureToken(length);
    }
    
    /**
     * Validate file uploads
     */
    public ValidationResult validateFileUpload(String fileName, String contentType, long fileSize) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return ValidationResult.invalid("File name is required");
        }
        
        // Check file extension
        String[] allowedExtensions = {".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"};
        boolean validExtension = false;
        for (String ext : allowedExtensions) {
            if (fileName.toLowerCase().endsWith(ext)) {
                validExtension = true;
                break;
            }
        }
        
        if (!validExtension) {
            return ValidationResult.invalid("File type not allowed");
        }
        
        // Check content type
        String[] allowedContentTypes = {
            "image/jpeg", "image/png", "image/gif", 
            "application/pdf", "application/msword", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        };
        
        boolean validContentType = false;
        for (String type : allowedContentTypes) {
            if (type.equals(contentType)) {
                validContentType = true;
                break;
            }
        }
        
        if (!validContentType) {
            return ValidationResult.invalid("Content type not allowed");
        }
        
        // Check file size (10MB limit)
        if (fileSize > 10 * 1024 * 1024) {
            return ValidationResult.invalid("File size exceeds limit");
        }
        
        return ValidationResult.valid();
    }
    
    private boolean containsSqlInjection(String input) {
        for (Pattern pattern : SQL_INJECTION_PATTERNS) {
            if (pattern.matcher(input).find()) {
                return true;
            }
        }
        return false;
    }
    
    private boolean containsXss(String input) {
        for (Pattern pattern : XSS_PATTERNS) {
            if (pattern.matcher(input).find()) {
                return true;
            }
        }
        return false;
    }
    
    public enum InputType {
        EMAIL, PHONE, ALPHANUMERIC, SAFE_TEXT, FREE_TEXT, PASSWORD, URL
    }
    
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static ValidationResult valid() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult invalid(String message) {
            return new ValidationResult(false, message);
        }
        
        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
    }
}
