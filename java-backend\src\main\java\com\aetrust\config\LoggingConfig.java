package com.aetrust.config;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy;
import ch.qos.logback.core.util.FileSize;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

@Slf4j
@Configuration
public class LoggingConfig {

    @Value("${aetrust.feature-flags.advanced-logging.enabled:true}")
    private boolean advancedLoggingEnabled;

    @Value("${logging.file.name:logs/aetrust-backend.log}")
    private String logFileName;

    @Value("${logging.file.max-size:100MB}")
    private String maxFileSize;

    @Value("${logging.file.max-history:30}")
    private int maxHistory;

    @Value("${logging.file.total-size-cap:1GB}")
    private String totalSizeCap;

    @PostConstruct
    public void configureLogging() {
        if (!advancedLoggingEnabled) {
            return;
        }

        try {
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();

            // Configure security audit logger
            configureSecurityAuditLogger(context);

            // Configure transaction logger
            configureTransactionLogger(context);

            // Configure error logger
            configureErrorLogger(context);

            log.info("Advanced logging configuration completed successfully");

        } catch (Exception e) {
            log.error("Failed to configure advanced logging: {}", e.getMessage());
        }
    }

    private void configureSecurityAuditLogger(LoggerContext context) {
        // Security audit log appender
        RollingFileAppender<ILoggingEvent> securityAppender = new RollingFileAppender<>();
        securityAppender.setContext(context);
        securityAppender.setName("SECURITY_AUDIT");
        securityAppender.setFile("logs/security-audit.log");

        // Rolling policy for security logs
        SizeAndTimeBasedRollingPolicy<ILoggingEvent> securityPolicy = new SizeAndTimeBasedRollingPolicy<>();
        securityPolicy.setContext(context);
        securityPolicy.setParent(securityAppender);
        securityPolicy.setFileNamePattern("logs/security-audit.%d{yyyy-MM-dd}.%i.log.gz");
        securityPolicy.setMaxFileSize(FileSize.valueOf("50MB"));
        securityPolicy.setMaxHistory(90); // keep 90 days of security logs
        securityPolicy.setTotalSizeCap(FileSize.valueOf("5GB"));
        securityPolicy.start();

        // Security log pattern
        PatternLayoutEncoder securityEncoder = new PatternLayoutEncoder();
        securityEncoder.setContext(context);
        securityEncoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [SECURITY] %logger{36} - %msg%n");
        securityEncoder.start();

        securityAppender.setRollingPolicy(securityPolicy);
        securityAppender.setEncoder(securityEncoder);
        securityAppender.start();

        // Create security logger
        Logger securityLogger = context.getLogger("com.aetrust.security");
        securityLogger.addAppender(securityAppender);
        securityLogger.setAdditive(false);
    }

    private void configureTransactionLogger(LoggerContext context) {
        // Transaction log appender
        RollingFileAppender<ILoggingEvent> transactionAppender = new RollingFileAppender<>();
        transactionAppender.setContext(context);
        transactionAppender.setName("TRANSACTION_AUDIT");
        transactionAppender.setFile("logs/transaction-audit.log");

        // Rolling policy for transaction logs
        SizeAndTimeBasedRollingPolicy<ILoggingEvent> transactionPolicy = new SizeAndTimeBasedRollingPolicy<>();
        transactionPolicy.setContext(context);
        transactionPolicy.setParent(transactionAppender);
        transactionPolicy.setFileNamePattern("logs/transaction-audit.%d{yyyy-MM-dd}.%i.log.gz");
        transactionPolicy.setMaxFileSize(FileSize.valueOf("100MB"));
        transactionPolicy.setMaxHistory(365); // keep 1 year of transaction logs
        transactionPolicy.setTotalSizeCap(FileSize.valueOf("10GB"));
        transactionPolicy.start();

        // Transaction log pattern
        PatternLayoutEncoder transactionEncoder = new PatternLayoutEncoder();
        transactionEncoder.setContext(context);
        transactionEncoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [TRANSACTION] %logger{36} - %msg%n");
        transactionEncoder.start();

        transactionAppender.setRollingPolicy(transactionPolicy);
        transactionAppender.setEncoder(transactionEncoder);
        transactionAppender.start();

        // Create transaction logger
        Logger transactionLogger = context.getLogger("com.aetrust.transaction");
        transactionLogger.addAppender(transactionAppender);
        transactionLogger.setAdditive(false);
    }

    private void configureErrorLogger(LoggerContext context) {
        // Error log appender
        RollingFileAppender<ILoggingEvent> errorAppender = new RollingFileAppender<>();
        errorAppender.setContext(context);
        errorAppender.setName("ERROR_LOG");
        errorAppender.setFile("logs/error.log");

        // Rolling policy for error logs
        SizeAndTimeBasedRollingPolicy<ILoggingEvent> errorPolicy = new SizeAndTimeBasedRollingPolicy<>();
        errorPolicy.setContext(context);
        errorPolicy.setParent(errorAppender);
        errorPolicy.setFileNamePattern("logs/error.%d{yyyy-MM-dd}.%i.log.gz");
        errorPolicy.setMaxFileSize(FileSize.valueOf("50MB"));
        errorPolicy.setMaxHistory(60); // keep 60 days of error logs
        errorPolicy.setTotalSizeCap(FileSize.valueOf("2GB"));
        errorPolicy.start();

        // Error log pattern with stack traces
        PatternLayoutEncoder errorEncoder = new PatternLayoutEncoder();
        errorEncoder.setContext(context);
        errorEncoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [ERROR] %logger{36} - %msg%n%ex");
        errorEncoder.start();

        errorAppender.setRollingPolicy(errorPolicy);
        errorAppender.setEncoder(errorEncoder);
        errorAppender.start();

        // Create error logger
        Logger errorLogger = context.getLogger("com.aetrust.error");
        errorLogger.addAppender(errorAppender);
        errorLogger.setAdditive(false);
    }

    // Utility methods for structured logging
    public static void logSecurityEvent(String event, String details, String userId, String ipAddress) {
        Logger securityLogger = (Logger) LoggerFactory.getLogger("com.aetrust.security");
        securityLogger.info("EVENT={} USER_ID={} IP={} DETAILS={}", event, userId, ipAddress, details);
    }

    public static void logTransactionEvent(String transactionId, String type, String amount, 
                                         String currency, String userId, String status) {
        Logger transactionLogger = (Logger) LoggerFactory.getLogger("com.aetrust.transaction");
        transactionLogger.info("TRANSACTION_ID={} TYPE={} AMOUNT={} CURRENCY={} USER_ID={} STATUS={}", 
                              transactionId, type, amount, currency, userId, status);
    }

    public static void logError(String component, String operation, String error, Exception exception) {
        Logger errorLogger = (Logger) LoggerFactory.getLogger("com.aetrust.error");
        errorLogger.error("COMPONENT={} OPERATION={} ERROR={}", component, operation, error, exception);
    }

    public static void logApiCall(String endpoint, String method, String userId, String ipAddress, 
                                 long duration, int statusCode) {
        Logger apiLogger = (Logger) LoggerFactory.getLogger("com.aetrust.api");
        apiLogger.info("ENDPOINT={} METHOD={} USER_ID={} IP={} DURATION={}ms STATUS={}", 
                      endpoint, method, userId, ipAddress, duration, statusCode);
    }

    public static void logBusinessEvent(String event, String entityType, String entityId, 
                                       String userId, String details) {
        Logger businessLogger = (Logger) LoggerFactory.getLogger("com.aetrust.business");
        businessLogger.info("EVENT={} ENTITY_TYPE={} ENTITY_ID={} USER_ID={} DETAILS={}", 
                           event, entityType, entityId, userId, details);
    }
}
