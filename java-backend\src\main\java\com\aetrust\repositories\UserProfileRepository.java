package com.aetrust.repositories;

import com.aetrust.models.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserProfileRepository extends JpaRepository<User.UserProfile, Long> {

    Optional<User.UserProfile> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<User.UserProfile> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    List<User.UserProfile> findByFirstNameContainingIgnoreCaseAndDeletedAtIsNull(String firstName);
    List<User.UserProfile> findByLastNameContainingIgnoreCaseAndDeletedAtIsNull(String lastName);
    List<User.UserProfile> findByCountryAndDeletedAtIsNull(String country);
    List<User.UserProfile> findByCityAndDeletedAtIsNull(String city);

    @Query("SELECT p FROM User$UserProfile p WHERE " +
           "(LOWER(p.firstName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(p.lastName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(p.city) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(p.country) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND p.deletedAt IS NULL")
    List<User.UserProfile> searchProfiles(@Param("search") String search);

    @Query("SELECT p FROM User$UserProfile p WHERE " +
           "p.firstName IS NOT NULL AND p.lastName IS NOT NULL " +
           "AND p.dateOfBirth IS NOT NULL AND p.deletedAt IS NULL")
    List<User.UserProfile> findCompleteProfiles();

    @Query("SELECT p FROM User$UserProfile p WHERE " +
           "(p.firstName IS NULL OR p.lastName IS NULL OR p.dateOfBirth IS NULL) " +
           "AND p.deletedAt IS NULL")
    List<User.UserProfile> findIncompleteProfiles();

    @Modifying
    @Query("UPDATE User$UserProfile p SET p.deletedAt = :deletedAt WHERE p.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserProfile p SET p.deletedAt = :deletedAt WHERE p.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
}
