package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.VerificationCode;
import com.aetrust.models.VerificationCode.VerificationType;
import com.aetrust.types.Types.*;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.repositories.VerificationCodeRepository;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class VerificationService {
    
    @Autowired
    private VerificationCodeRepository verificationCodeRepository;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Autowired
    private NotificationService notificationService;
    
    private static final SecureRandom secureRandom = new SecureRandom();
    private static final int MAX_VERIFICATION_ATTEMPTS = 5;
    private static final int CODE_EXPIRY_MINUTES = 15;
    
    public static class VerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String nextStep;
        private String verificationCode;
        
        public static VerificationResult success(String message, String nextStep) {
            return new VerificationResult(true, message, null, nextStep, null);
        }
        
        public static VerificationResult error(String message, String errorCode) {
            return new VerificationResult(false, message, errorCode, null, null);
        }
        
        public static VerificationResult withCode(String message, String nextStep, String code) {
            return new VerificationResult(true, message, null, nextStep, code);
        }

        public VerificationResult() {}

        public VerificationResult(boolean success, String message, String errorCode, String nextStep, String verificationCode) {
            this.success = success;
            this.message = message;
            this.errorCode = errorCode;
            this.nextStep = nextStep;
            this.verificationCode = verificationCode;
        }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        public String getNextStep() { return nextStep; }
        public void setNextStep(String nextStep) { this.nextStep = nextStep; }
        public String getVerificationCode() { return verificationCode; }
        public void setVerificationCode(String verificationCode) { this.verificationCode = verificationCode; }
    }
    
   
    private String generateSecureVerificationCode() {
        int code = secureRandom.nextInt(1000000);
        return String.format("%06d", code);
    }
    
    
    @Transactional
    public VerificationResult sendPhoneVerification(String phone, String userIdentifier) {
        try {

            if (isRateLimited(userIdentifier, VerificationType.SMS)) {
                log.warn("Rate limit exceeded for phone verification: {}", 
                    cryptoUtils.maskSensitiveData(phone, 3));
                return VerificationResult.error("Too many verification attempts", "RATE_LIMIT_EXCEEDED");
            }
            
            invalidateExistingCodes(userIdentifier, VerificationType.SMS);
            String code = generateSecureVerificationCode();

            VerificationCode verificationCode = new VerificationCode();
            verificationCode.setUserIdentifier(userIdentifier);
            verificationCode.setCode(cryptoUtils.hashVerificationCode(code));
            verificationCode.setVerificationType(VerificationType.SMS);
            verificationCode.setExpiresAt(LocalDateTime.now().plusMinutes(CODE_EXPIRY_MINUTES));
            verificationCode.setAttemptsCount(0);
            verificationCode.setCreatedAt(LocalDateTime.now());
            
            verificationCodeRepository.save(verificationCode);
            
            NotificationService.NotificationResult smsResult = 
                notificationService.sendSms(phone, "Your verification code is: " + code);
            
            if (!smsResult.isSuccess()) {
                log.warn("SMS service unavailable for phone: {}", 
                    cryptoUtils.maskSensitiveData(phone, 3));
                // Return code
                return VerificationResult.withCode("SMS service unavailable", "verify_phone", code);
            }
            
            log.info("Phone verification code sent to: {}", 
                cryptoUtils.maskSensitiveData(phone, 3));
            
            return VerificationResult.success("Verification code sent", "verify_phone");
            
        } catch (Exception error) {
            log.error("Error sending phone verification: {}", error.getMessage());
            return VerificationResult.error("Failed to send verification code", "INTERNAL_ERROR");
        }
    }
    
    
    @Transactional
    public VerificationResult sendEmailVerification(String email, String userIdentifier) {
        try {

            if (isRateLimited(userIdentifier, VerificationType.EMAIL)) {
                log.warn("Rate limit exceeded for email verification: {}", 
                    cryptoUtils.maskSensitiveData(email, 2));
                return VerificationResult.error("Too many verification attempts", "RATE_LIMIT_EXCEEDED");
            }
        
            invalidateExistingCodes(userIdentifier, VerificationType.EMAIL);
            
            String code = generateSecureVerificationCode();
            
            VerificationCode verificationCode = new VerificationCode();
            verificationCode.setUserIdentifier(userIdentifier);
            verificationCode.setCode(cryptoUtils.hashVerificationCode(code));
            verificationCode.setVerificationType(VerificationType.EMAIL);
            verificationCode.setExpiresAt(LocalDateTime.now().plusMinutes(CODE_EXPIRY_MINUTES));
            verificationCode.setAttemptsCount(0);
            verificationCode.setCreatedAt(LocalDateTime.now());
            
            verificationCodeRepository.save(verificationCode);
            
            NotificationService.NotificationResult emailResult = 
                notificationService.sendEmail(email, "Verification Code", 
                    "Your verification code is: " + code);
            
            if (!emailResult.isSuccess()) {
                log.warn("Email service unavailable for: {}", 
                    cryptoUtils.maskSensitiveData(email, 2));

                
                return VerificationResult.withCode("Email service unavailable", "verify_email", code);
            }
            
            log.info("Email verification code sent to: {}", 
                cryptoUtils.maskSensitiveData(email, 2));
            
            return VerificationResult.success("Verification code sent", "verify_email");
            
        } catch (Exception error) {
            log.error("Error sending email verification: {}", error.getMessage());
            return VerificationResult.error("Failed to send verification code", "INTERNAL_ERROR");
        }
    }
    
  
    @Transactional
    public VerificationResult verifyCode(String userIdentifier, String providedCode, VerificationType type) {
        try {
            Optional<VerificationCode> codeOpt = verificationCodeRepository
                .findActiveCode(userIdentifier, type, LocalDateTime.now());
            
            if (!codeOpt.isPresent()) {
                log.warn("Verification code not found or expired for user: {}", 
                    cryptoUtils.maskSensitiveData(userIdentifier, 3));
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }
            
            VerificationCode verificationCode = codeOpt.get();
            
            verificationCode.incrementAttempts();
            
            if (verificationCode.getAttemptsCount() > MAX_VERIFICATION_ATTEMPTS) {
                verificationCodeRepository.save(verificationCode);
                log.warn("Too many verification attempts for user: {}", 
                    cryptoUtils.maskSensitiveData(userIdentifier, 3));
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }
            
            if (!cryptoUtils.verifyVerificationCode(providedCode, verificationCode.getCode())) {
                verificationCodeRepository.save(verificationCode);
                log.warn("Invalid verification code for user: {}", 
                    cryptoUtils.maskSensitiveData(userIdentifier, 3));
                return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
            }
            
            // Mark code as used to prevent reuse
            verificationCode.markAsUsed();
            verificationCodeRepository.save(verificationCode);
            
            log.info("Verification successful for user: {}", 
                cryptoUtils.maskSensitiveData(userIdentifier, 3));
            
            return VerificationResult.success("Verification successful", "verified");
            
        } catch (Exception error) {
            log.error("Error verifying code: {}", error.getMessage());
            return VerificationResult.error("Verification failed", "VERIFICATION_FAILED");
        }
    }
    
    
    public boolean isRateLimited(String userIdentifier, VerificationType type) {
        LocalDateTime since = LocalDateTime.now().minusHours(1);
        long recentAttempts = verificationCodeRepository.countRecentAttempts(userIdentifier, type, since);
        return recentAttempts >= 5;
    }
    
  
    private void invalidateExistingCodes(String userIdentifier, VerificationType type) {
        verificationCodeRepository.invalidateExistingCodes(userIdentifier, type, LocalDateTime.now());
    }
}
