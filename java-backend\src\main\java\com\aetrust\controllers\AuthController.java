package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.AuthService;
import com.aetrust.services.SecurityService;
import com.aetrust.services.ValidationService;
import com.aetrust.utils.ControllerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/auth")
@Validated
public class AuthController {
    
    @Autowired
    private AuthService authService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private Environment environment;

    @Autowired
    private ControllerUtils controllerUtils;

    @Autowired
    private ValidationService validationService;

    @Value("${aetrust.security.max-login-attempts:5}")
    private int maxLoginAttempts;

    @Value("${aetrust.security.lockout-duration:900000}")
    private long lockoutDuration;
    
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/login");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            SecurityService.BruteForceResult bruteForce = securityService.checkBruteForceProtection(
                request.getEmail(), "user");
            
            if (!bruteForce.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error(bruteForce.getReason(), "ACCOUNT_LOCKED"));
            }
            
            AuthService.LoginResult result = authService.login(request, ipAddress, userAgent);
            
            if (!result.isSuccess()) {
                securityService.recordFailedAttempt(request.getEmail(), "user");
                securityService.recordFailedAttempt(ipAddress, "ip");

                log.warn("Login failed for email: {}, IP: {}, reason: {}",
                    request.getEmail(), ipAddress, result.getMessage());

                return ResponseEntity.status(401).body(
                    ApiResponse.error("Authentication failed", "AUTH_FAILED"));
            }
            
            securityService.clearFailedAttempts(request.getEmail(), "user");
            securityService.clearFailedAttempts(ipAddress, "ip");
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("access_token", result.getAccessToken());
            responseData.put("refresh_token", result.getRefreshToken());
            responseData.put("user", result.getUserData());
            responseData.put("expires_in", 3600);
            responseData.put("token_type", "Bearer");
            
            if (result.isRequires2FA()) {
                responseData.put("requires_2fa", true);
                responseData.put("challenge_token", result.getChallengeToken());
            }
            
            return ResponseEntity.ok(
                ApiResponse.success("login successful", responseData));
                
        } catch (Exception error) {
            log.error("Error during login: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("login failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshToken(
            @Valid @RequestBody RefreshTokenRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/refresh");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many refresh attempts", "RATE_LIMIT_EXCEEDED"));
            }

            AuthService.RefreshResult result = authService.refreshToken(request.getRefreshToken());

            if (!result.isSuccess()) {
                log.warn("Token refresh failed for IP: {}, reason: {}", ipAddress, result.getMessage());
                return ResponseEntity.status(401).body(
                    ApiResponse.error("Token refresh failed", "REFRESH_FAILED"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("access_token", result.getAccessToken());
            responseData.put("refresh_token", result.getRefreshToken());
            responseData.put("expires_in", 3600);
            responseData.put("token_type", "Bearer");

            return ResponseEntity.ok(
                ApiResponse.success("Token refreshed successfully", responseData));

        } catch (Exception error) {
            log.error("Error refreshing token: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("Token refresh failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Map<String, Object>>> logout(
            @Valid @RequestBody LogoutRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/logout");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many logout attempts", "RATE_LIMIT_EXCEEDED"));
            }

            AuthService.LogoutResult result = authService.logout(
                request.getAccessToken(), request.getRefreshToken());

            if (!result.isSuccess()) {
                log.warn("Logout failed for IP: {}, reason: {}", ipAddress, result.getMessage());
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("logged_out", true);
            responseData.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(
                ApiResponse.success("Logout successful", responseData));

        } catch (Exception error) {
            log.error("Error during logout: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("Logout failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> forgotPassword(
            @Valid @RequestBody ForgotPasswordRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/forgot-password");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many password reset attempts", "RATE_LIMIT_EXCEEDED"));
            }

            SecurityService.RateLimitResult emailRateLimit = securityService.checkRateLimit(
                request.getEmail(), "email", "/auth/forgot-password");

            if (!emailRateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many password reset attempts for this email", "RATE_LIMIT_EXCEEDED"));
            }

            AuthService.ForgotPasswordResult result = authService.forgotPassword(request.getEmail());

            // Always return success
            // Log
            if (!result.isSuccess()) {
                log.warn("Password reset failed for email: {}, IP: {}, reason: {}",
                    request.getEmail(), ipAddress, result.getMessage());
            } else {
                log.info("Password reset email sent for email: {}, IP: {}",
                    request.getEmail(), ipAddress);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("email_sent", true);
            responseData.put("message", "If the email exists, password reset instructions have been sent");
            responseData.put("reset_token_expires", 900); // 15 minutes

            return ResponseEntity.ok(
                ApiResponse.success("Password reset request processed", responseData));

        } catch (Exception error) {
            log.error("Error processing password reset request: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("Password reset request failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> resetPassword(
            @Valid @RequestBody ResetPasswordRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/reset-password");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many password reset attempts", "RATE_LIMIT_EXCEEDED"));
            }

            AuthService.ResetPasswordResult result = authService.resetPassword(
                request.getToken(), request.getNewPassword());

            if (!result.isSuccess()) {
                log.warn("Password reset failed for IP: {}, token: {}, reason: {}",
                    ipAddress, request.getToken().substring(0, 8) + "...", result.getMessage());
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Password reset failed", "RESET_FAILED"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("password_reset", true);
            responseData.put("message", "Password has been reset successfully");
            responseData.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(
                ApiResponse.success("Password reset successful", responseData));

        } catch (Exception error) {
            log.error("Error resetting password: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("Password reset failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "ok");
        healthData.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(
            ApiResponse.success("auth service is running", healthData));
    }


    // @GetMapping("/config")
    // public ResponseEntity<ApiResponse<Map<String, Object>>> getPublicConfig() {
    //     try {
    //         Map<String, Object> configData = new HashMap<>();
    //         configData.put("service", "auth-service");
    //         configData.put("version", "1.0.0");
    //         configData.put("environment", environment.getProperty("ENVIRONMENT", "development"));

    //         Map<String, Object> validation = new HashMap<>();
    //         validation.put("passwordMinLength", 8);
    //         validation.put("passwordRequireUppercase", true);
    //         validation.put("passwordRequireLowercase", true);
    //         validation.put("passwordRequireDigit", true);
    //         validation.put("passwordRequireSpecialChar", true);
    //         validation.put("emailFormat", "RFC 5322 compliant");
    //         validation.put("phoneFormat", "International format (+country code)");

    //         configData.put("validation", validation);

    //         Map<String, Object> features = new HashMap<>();
    //         features.put("twoFactorAuth", true);
    //         features.put("biometricAuth", true);
    //         features.put("socialLogin", false);
    //         features.put("passwordReset", true);

    //         configData.put("features", features);

    //         return ResponseEntity.ok(
    //             ApiResponse.success("Public configuration retrieved", configData));

    //     } catch (Exception error) {
    //         log.error("Error getting public auth config: {}", error.getMessage());
    //         return ResponseEntity.status(500).body(
    //             ApiResponse.error("Failed to retrieve configuration", "CONFIG_ERROR"));
    //     }
    // }
    

}
