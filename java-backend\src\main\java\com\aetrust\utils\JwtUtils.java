package com.aetrust.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.aetrust.types.Types.UserRole;
import com.aetrust.types.Types.KycStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class JwtUtils {
    
    @Value("${spring.security.jwt.secret}")
    private String jwtSecret;
    
    @Value("${spring.security.jwt.expiration}")
    private long jwtExpiration;
    
    @Value("${spring.security.jwt.refresh-expiration}")
    private long refreshExpiration;
    
    private static final String ISSUER = "aetrust-backend";
    
  
    public String generateToken(String userId, String email, UserRole role, boolean isVerified, KycStatus kycStatus) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + jwtExpiration);
            
            return JWT.create()
                    .withIssuer(ISSUER)
                    .withSubject(userId)
                    .withClaim("id", userId)
                    .withClaim("email", email)
                    .withClaim("role", role.getValue())
                    .withClaim("isVerified", isVerified)
                    .withClaim("kycStatus", kycStatus.getValue())
                    .withIssuedAt(now)
                    .withExpiresAt(expiryDate)
                    .sign(algorithm);
                    
        } catch (JWTCreationException error) {
            log.error("Error creating JWT token: {}", error.getMessage());
            throw new RuntimeException("Failed to create JWT token");
        }
    }
    
  
    public String generateRefreshToken(String userId) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + refreshExpiration);
            
            return JWT.create()
                    .withIssuer(ISSUER)
                    .withSubject(userId)
                    .withClaim("type", "refresh")
                    .withClaim("userId", userId)
                    .withIssuedAt(now)
                    .withExpiresAt(expiryDate)
                    .sign(algorithm);
                    
        } catch (JWTCreationException error) {
            log.error("Error creating refresh token: {}", error.getMessage());
            throw new RuntimeException("Failed to create refresh token");
        }
    }
    
    
    public DecodedJWT verifyToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(ISSUER)
                    .build();
                    
            return verifier.verify(token);
            
        } catch (JWTVerificationException error) {
            log.warn("JWT verification failed: {}", error.getMessage());
            throw new RuntimeException("Invalid or expired token");
        }
    }
    
   
    public UserPayload extractUserPayload(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            
            return UserPayload.builder()
                    .id(decodedJWT.getClaim("id").asString())
                    .email(decodedJWT.getClaim("email").asString())
                    .role(UserRole.valueOf(decodedJWT.getClaim("role").asString().toUpperCase()))
                    .isVerified(decodedJWT.getClaim("isVerified").asBoolean())
                    .kycStatus(KycStatus.valueOf(decodedJWT.getClaim("kycStatus").asString().toUpperCase()))
                    .iat(decodedJWT.getIssuedAt().getTime())
                    .exp(decodedJWT.getExpiresAt().getTime())
                    .build();
                    
        } catch (Exception error) {
            log.error("Error extracting user payload: {}", error.getMessage());
            throw new RuntimeException("Failed to extract user data from token");
        }
    }
    
   
    public boolean isTokenExpired(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getExpiresAt().before(new Date());
        } catch (Exception error) {
            return true;
        }
    }
    
  
    public Date getExpirationDate(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getExpiresAt();
        } catch (Exception error) {
            return null;
        }
    }
    
   
    public String getUserIdFromToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            return decodedJWT.getClaim("id").asString();
        } catch (Exception error) {
            return null;
        }
    }
    
    
    public static class UserPayload {
        private String id;
        private String email;
        private UserRole role;
        private boolean isVerified;
        private KycStatus kycStatus;
        private Long iat;
        private Long exp;
        
        // builder pattern
        public static UserPayloadBuilder builder() {
            return new UserPayloadBuilder();
        }
        
        public static class UserPayloadBuilder {
            private String id;
            private String email;
            private UserRole role;
            private boolean isVerified;
            private KycStatus kycStatus;
            private Long iat;
            private Long exp;
            
            public UserPayloadBuilder id(String id) {
                this.id = id;
                return this;
            }
            
            public UserPayloadBuilder email(String email) {
                this.email = email;
                return this;
            }
            
            public UserPayloadBuilder role(UserRole role) {
                this.role = role;
                return this;
            }
            
            public UserPayloadBuilder isVerified(boolean isVerified) {
                this.isVerified = isVerified;
                return this;
            }
            
            public UserPayloadBuilder kycStatus(KycStatus kycStatus) {
                this.kycStatus = kycStatus;
                return this;
            }
            
            public UserPayloadBuilder iat(Long iat) {
                this.iat = iat;
                return this;
            }
            
            public UserPayloadBuilder exp(Long exp) {
                this.exp = exp;
                return this;
            }
            
            public UserPayload build() {
                UserPayload payload = new UserPayload();
                payload.id = this.id;
                payload.email = this.email;
                payload.role = this.role;
                payload.isVerified = this.isVerified;
                payload.kycStatus = this.kycStatus;
                payload.iat = this.iat;
                payload.exp = this.exp;
                return payload;
            }
        }
        
        // getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public UserRole getRole() { return role; }
        public void setRole(UserRole role) { this.role = role; }
        
        public boolean isVerified() { return isVerified; }
        public void setVerified(boolean verified) { isVerified = verified; }
        
        public KycStatus getKycStatus() { return kycStatus; }
        public void setKycStatus(KycStatus kycStatus) { this.kycStatus = kycStatus; }
        
        public Long getIat() { return iat; }
        public void setIat(Long iat) { this.iat = iat; }
        
        public Long getExp() { return exp; }
        public void setExp(Long exp) { this.exp = exp; }
    }
}
