package com.aetrust.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import java.lang.annotation.*;
import java.util.regex.Pattern;

public class Validation {
    
    @Documented
    @Constraint(validatedBy = PhoneValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidPhone {
        String message() default "invalid phone number format";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    @Documented
    @Constraint(validatedBy = PasswordValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidPassword {
        String message() default "password must be at least 8 characters with uppercase, lowercase, number and special character";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    @Documented
    @Constraint(validatedBy = AmountValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidAmount {
        String message() default "invalid amount";
        double min() default 0.01;
        double max() default 1000000.0;
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    @Documented
    @Constraint(validatedBy = CurrencyValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidCurrency {
        String message() default "invalid currency";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    @Documented
    @Constraint(validatedBy = PlatformValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidPlatform {
        String message() default "invalid platform";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    @Documented
    @Constraint(validatedBy = PinValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidPin {
        String message() default "PIN must be 4 digits";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    @Documented
    @Constraint(validatedBy = VerificationCodeValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidVerificationCode {
        String message() default "verification code must be 6 digits";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }

    @Documented
    @Constraint(validatedBy = OtpValidator.class)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ValidOtp {
        String message() default "OTP code must be exactly 6 digits";
        Class<?>[] groups() default {};
        Class<? extends Payload>[] payload() default {};
    }
    
    public static class PhoneValidator implements ConstraintValidator<ValidPhone, String> {
        private static final Pattern PHONE_PATTERN = Pattern.compile("^\\+[1-9]\\d{1,14}$");
        
        @Override
        public boolean isValid(String phone, ConstraintValidatorContext context) {
            if (phone == null || phone.trim().isEmpty()) {
                return false;
            }
            return PHONE_PATTERN.matcher(phone.trim()).matches();
        }
    }
    
    public static class PasswordValidator implements ConstraintValidator<ValidPassword, String> {
        private static final Pattern PASSWORD_PATTERN = Pattern.compile(
            "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])"
        );
        
        @Override
        public boolean isValid(String password, ConstraintValidatorContext context) {
            if (password == null || password.length() < 8) {
                return false;
            }
            return PASSWORD_PATTERN.matcher(password).find();
        }
    }
    
    public static class AmountValidator implements ConstraintValidator<ValidAmount, Number> {
        private double min;
        private double max;
        
        @Override
        public void initialize(ValidAmount constraintAnnotation) {
            this.min = constraintAnnotation.min();
            this.max = constraintAnnotation.max();
        }
        
        @Override
        public boolean isValid(Number amount, ConstraintValidatorContext context) {
            if (amount == null) {
                return false;
            }
            double value = amount.doubleValue();
            return value >= min && value <= max;
        }
    }
    
    public static class CurrencyValidator implements ConstraintValidator<ValidCurrency, String> {
        private static final Pattern CURRENCY_PATTERN = Pattern.compile("^(USD|EUR|GBP|RWF|KES|UGX|TZS)$");
        
        @Override
        public boolean isValid(String currency, ConstraintValidatorContext context) {
            if (currency == null || currency.trim().isEmpty()) {
                return false;
            }
            return CURRENCY_PATTERN.matcher(currency.trim().toUpperCase()).matches();
        }
    }
    
    public static class PlatformValidator implements ConstraintValidator<ValidPlatform, String> {
        private static final Pattern PLATFORM_PATTERN = Pattern.compile("^(web|app|mobile_web)$");
        
        @Override
        public boolean isValid(String platform, ConstraintValidatorContext context) {
            if (platform == null || platform.trim().isEmpty()) {
                return false;
            }
            return PLATFORM_PATTERN.matcher(platform.trim().toLowerCase()).matches();
        }
    }
    
    public static class PinValidator implements ConstraintValidator<ValidPin, String> {
        private static final Pattern PIN_PATTERN = Pattern.compile("^\\d{4}$");
        
        @Override
        public boolean isValid(String pin, ConstraintValidatorContext context) {
            if (pin == null || pin.trim().isEmpty()) {
                return false;
            }
            return PIN_PATTERN.matcher(pin.trim()).matches();
        }
    }
    
    public static class VerificationCodeValidator implements ConstraintValidator<ValidVerificationCode, String> {
        private static final Pattern CODE_PATTERN = Pattern.compile("^\\d{6}$");

        @Override
        public boolean isValid(String code, ConstraintValidatorContext context) {
            if (code == null || code.trim().isEmpty()) {
                return false;
            }
            return CODE_PATTERN.matcher(code.trim()).matches();
        }
    }

    public static class OtpValidator implements ConstraintValidator<ValidOtp, String> {
        private static final Pattern OTP_PATTERN = Pattern.compile("^\\d{6}$");

        @Override
        public boolean isValid(String otp, ConstraintValidatorContext context) {
            if (otp == null || otp.trim().isEmpty()) {
                return false;
            }
            return OTP_PATTERN.matcher(otp.trim()).matches();
        }
    }
}
