package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PostgresService {
    
    private final DataSource dataSource;
    
    @Value("${spring.datasource.url}")
    private String dbUrl;
    
    @Value("${spring.datasource.username}")
    private String dbUsername;
    
    @Autowired
    public PostgresService(DataSource dataSource) {
        this.dataSource = dataSource;
        log.info("PostgresService initialized");
    }
    
    // get all records from a table 
    public List<Map<String, Object>> getAllRecords(String tableName) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection()) {
            String sql = "SELECT * FROM " + tableName;
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
                
                // log first few records
                if (results.size() <= 5) {
                    log.debug("Record from {}: {}", tableName, row);
                }
            }
            
            log.info("Retrieved {} records from table: {}", results.size(), tableName);
            
        } catch (SQLException e) {
            handleSqlException(e, "getAllRecords from " + tableName);
            throw e;
        }
        
        return results;
    }
    
    // get filtered records
    public List<Map<String, Object>> getFilteredRecords(String tableName, String whereColumn, Object whereValue) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection()) {
            String sql = "SELECT * FROM " + tableName + " WHERE " + whereColumn + " = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setObject(1, whereValue);
            
            ResultSet rs = stmt.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
            
            log.info("Retrieved {} filtered records from {}", results.size(), tableName);
            
        } catch (SQLException e) {
            handleSqlException(e, "getFilteredRecords from " + tableName);
            throw e;
        }
        
        return results;
    }
    
    // insert record
    public int insertRecord(String tableName, Map<String, Object> data) throws SQLException {
        try (Connection conn = dataSource.getConnection()) {
            StringBuilder columns = new StringBuilder();
            StringBuilder placeholders = new StringBuilder();
            List<Object> values = new ArrayList<>();
            
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                if (columns.length() > 0) {
                    columns.append(", ");
                    placeholders.append(", ");
                }
                columns.append(entry.getKey());
                placeholders.append("?");
                values.add(entry.getValue());
            }
            
            String sql = "INSERT INTO " + tableName + "(" + columns + ") VALUES (" + placeholders + ")";
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            for (int i = 0; i < values.size(); i++) {
                stmt.setObject(i + 1, values.get(i));
            }
            
            int insertedRows = stmt.executeUpdate();
            log.info("Inserted {} record(s) into {}", insertedRows, tableName);
            
            return insertedRows;
            
        } catch (SQLException e) {
            handleSqlException(e, "insertRecord into " + tableName);
            throw e;
        }
    }
    
    // update record
    public int updateRecord(String tableName, Map<String, Object> setData, String whereColumn, Object whereValue) throws SQLException {
        try (Connection conn = dataSource.getConnection()) {
            StringBuilder setClause = new StringBuilder();
            List<Object> values = new ArrayList<>();
            
            for (Map.Entry<String, Object> entry : setData.entrySet()) {
                if (setClause.length() > 0) {
                    setClause.append(", ");
                }
                setClause.append(entry.getKey()).append(" = ?");
                values.add(entry.getValue());
            }
            
            String sql = "UPDATE " + tableName + " SET " + setClause + " WHERE " + whereColumn + " = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            for (int i = 0; i < values.size(); i++) {
                stmt.setObject(i + 1, values.get(i));
            }
            stmt.setObject(values.size() + 1, whereValue);
            
            int updatedRows = stmt.executeUpdate();
            log.info("Updated {} record(s) in {}", updatedRows, tableName);
            
            return updatedRows;
            
        } catch (SQLException e) {
            handleSqlException(e, "updateRecord in " + tableName);
            throw e;
        }
    }
    
    // delete record
    public int deleteRecord(String tableName, String whereColumn, Object whereValue) throws SQLException {
        try (Connection conn = dataSource.getConnection()) {
            String sql = "DELETE FROM " + tableName + " WHERE " + whereColumn + " = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setObject(1, whereValue);
            
            int deletedRows = stmt.executeUpdate();
            log.info("Deleted {} record(s) from {}", deletedRows, tableName);
            
            return deletedRows;
            
        } catch (SQLException e) {
            handleSqlException(e, "deleteRecord from " + tableName);
            throw e;
        }
    }
    
    // execute custom query with param
    public List<Map<String, Object>> executeCustomQuery(String sql, Object... params) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection()) {
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            
            ResultSet rs = stmt.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
            
            log.info("Custom query returned {} records", results.size());
            
        } catch (SQLException e) {
            handleSqlException(e, "executeCustomQuery");
            throw e;
        }
        
        return results;
    }
    
    // execute custom update/insert/delete
    public int executeCustomUpdate(String sql, Object... params) throws SQLException {
        try (Connection conn = dataSource.getConnection()) {
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            
            int affectedRows = stmt.executeUpdate();
            log.info("Custom update affected {} rows", affectedRows);
            
            return affectedRows;
            
        } catch (SQLException e) {
            handleSqlException(e, "executeCustomUpdate");
            throw e;
        }
    }
    
    // batch operations
    public int[] executeBatchInsert(String tableName, List<Map<String, Object>> dataList) throws SQLException {
        if (dataList.isEmpty()) {
            return new int[0];
        }
        
        try (Connection conn = dataSource.getConnection()) {
            // use first record to build sql
            Map<String, Object> firstRecord = dataList.get(0);
            StringBuilder columns = new StringBuilder();
            StringBuilder placeholders = new StringBuilder();
            
            for (String column : firstRecord.keySet()) {
                if (columns.length() > 0) {
                    columns.append(", ");
                    placeholders.append(", ");
                }
                columns.append(column);
                placeholders.append("?");
            }
            
            String sql = "INSERT INTO " + tableName + "(" + columns + ") VALUES (" + placeholders + ")";
            PreparedStatement stmt = conn.prepareStatement(sql);
            
            for (Map<String, Object> data : dataList) {
                int paramIndex = 1;
                for (String column : firstRecord.keySet()) {
                    stmt.setObject(paramIndex++, data.get(column));
                }
                stmt.addBatch();
            }
            
            int[] results = stmt.executeBatch();
            log.info("Batch insert completed: {} records processed", results.length);
            
            return results;
            
        } catch (SQLException e) {
            handleSqlException(e, "executeBatchInsert into " + tableName);
            throw e;
        }
    }
    
    // get db connection info
    public Map<String, Object> getConnectionInfo() throws SQLException {
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            
            Map<String, Object> info = new HashMap<>();
            info.put("database_product_name", metaData.getDatabaseProductName());
            info.put("database_product_version", metaData.getDatabaseProductVersion());
            info.put("driver_name", metaData.getDriverName());
            info.put("driver_version", metaData.getDriverVersion());
            info.put("url", maskUrl(metaData.getURL()));
            info.put("username", dbUsername);
            info.put("max_connections", metaData.getMaxConnections());
            info.put("auto_commit", conn.getAutoCommit());
            info.put("transaction_isolation", conn.getTransactionIsolation());
            
            return info;
            
        } catch (SQLException e) {
            handleSqlException(e, "getConnectionInfo");
            throw e;
        }
    }
    
    // handle sql exceptions with proper error code
    private void handleSqlException(SQLException e, String operation) {
        String errorCode = e.getSQLState();
        log.error("SQL operation '{}' failed with error code: {}", operation, errorCode, e);
        
        // handle specific postgresql error codes
        switch (errorCode) {
            case "08000": // connection_exception
                log.error("Connection exception - may need to retry connection");
                break;
            case "08003": // connection_does_not_exist
                log.error("Connection does not exist");
                break;
            case "08006": // connection_failure
                log.error("Connection failure");
                break;
            case "42601": // syntax_error
                log.error("SQL syntax error - check your query");
                break;
            case "42501": // insufficient_privilege
                log.error("Insufficient privileges for operation");
                break;
            case "42P01": // undefined_table
                log.error("Table does not exist");
                break;
            case "23505": // unique_violation
                log.warn("Unique constraint violation");
                break;
            case "23503": // foreign_key_violation
                log.warn("Foreign key constraint violation");
                break;
            case "23502": // not_null_violation
                log.warn("Not null constraint violation");
                break;
            case "23514": // check_violation
                log.warn("Check constraint violation");
                break;
            default:
                log.error("Unhandled SQL error code: {} - {}", errorCode, e.getMessage());
        }
    }
    
    private String maskUrl(String url) {
        if (url == null) return "null";
        return url.replaceAll("://([^:]+):([^@]+)@", "://$1:****@");
    }
}
