package com.aetrust.repositories;

import com.aetrust.models.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserSecurityRepository extends JpaRepository<User.UserSecurity, Long> {

    Optional<User.UserSecurity> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<User.UserSecurity> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    Optional<User.UserSecurity> findByPasswordResetTokenAndDeletedAtIsNull(String token);
    Optional<User.UserSecurity> findByEmailVerificationTokenAndDeletedAtIsNull(String token);
    Optional<User.UserSecurity> findByPhoneVerificationCodeAndDeletedAtIsNull(String code);

    List<User.UserSecurity> findByTwoFactorEnabledAndDeletedAtIsNull(boolean enabled);
    List<User.UserSecurity> findByBiometricEnabledAndDeletedAtIsNull(boolean enabled);
    List<User.UserSecurity> findByTransactionPinSetAndDeletedAtIsNull(boolean pinSet);

    @Query("SELECT s FROM User$UserSecurity s WHERE s.lockedUntil > :currentTime AND s.deletedAt IS NULL")
    List<User.UserSecurity> findLockedAccounts(@Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT s FROM User$UserSecurity s WHERE s.pinLockedUntil > :currentTime AND s.deletedAt IS NULL")
    List<User.UserSecurity> findPinLockedAccounts(@Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT s FROM User$UserSecurity s WHERE s.loginAttempts >= :maxAttempts AND s.deletedAt IS NULL")
    List<User.UserSecurity> findAccountsWithExcessiveLoginAttempts(@Param("maxAttempts") Integer maxAttempts);

    @Query("SELECT s FROM User$UserSecurity s WHERE s.lastLogin >= :date AND s.deletedAt IS NULL")
    List<User.UserSecurity> findRecentLogins(@Param("date") LocalDateTime date);

    @Query("SELECT COUNT(s) FROM User$UserSecurity s WHERE s.twoFactorEnabled = true AND s.deletedAt IS NULL")
    long countUsersWithTwoFactorEnabled();

    @Query("SELECT COUNT(s) FROM User$UserSecurity s WHERE s.biometricEnabled = true AND s.deletedAt IS NULL")
    long countUsersWithBiometricEnabled();

    @Modifying
    @Query("UPDATE User$UserSecurity s SET s.deletedAt = :deletedAt WHERE s.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserSecurity s SET s.deletedAt = :deletedAt WHERE s.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserSecurity s SET s.loginAttempts = 0, s.lockedUntil = null WHERE s.userId = :userId")
    void resetLoginAttempts(@Param("userId") Long userId);

    @Modifying
    @Query("UPDATE User$UserSecurity s SET s.pinAttempts = 0, s.pinLockedUntil = null WHERE s.userId = :userId")
    void resetPinAttempts(@Param("userId") Long userId);
}
