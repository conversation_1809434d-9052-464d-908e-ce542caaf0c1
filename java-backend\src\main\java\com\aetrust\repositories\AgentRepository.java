package com.aetrust.repositories;

import com.aetrust.models.Agent;
import com.aetrust.types.Types.AgentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface AgentRepository extends JpaRepository<Agent, Long> {
    
    Optional<Agent> findByUserId(String userId);
    Optional<Agent> findByAgentCode(String agentCode);
    boolean existsByUserId(String userId);
    boolean existsByAgentCode(String agentCode);
    List<Agent> findByStatus(AgentStatus status);
    List<Agent> findByStatusAndCreatedAtAfter(AgentStatus status, LocalDateTime date);
    
    @Query("SELECT a FROM Agent a WHERE a.status = :status AND a.cashBalance > :minBalance")
    List<Agent> findActiveAgentsWithBalance(@Param("status") AgentStatus status, @Param("minBalance") Double minBalance);
    
    @Query("SELECT a FROM Agent a WHERE " +
           "(LOWER(a.businessName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(a.agentCode) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND a.status = :status")
    List<Agent> searchAgentsByStatus(@Param("search") String search, @Param("status") AgentStatus status);
    
    @Query("SELECT a FROM Agent a WHERE " +
           "LOWER(a.businessName) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(a.agentCode) LIKE LOWER(CONCAT('%', :search, '%'))")
    List<Agent> searchAgents(@Param("search") String search);
    
    @Query("SELECT COUNT(a) FROM Agent a WHERE a.status = :status")
    long countByStatus(@Param("status") AgentStatus status);
    
    @Query("SELECT a FROM Agent a WHERE a.status = :status ORDER BY a.totalCommissions DESC")
    List<Agent> findTopAgentsByCommissions(@Param("status") AgentStatus status);
}
