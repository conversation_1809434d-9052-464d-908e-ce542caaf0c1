package com.aetrust.validation;

import com.aetrust.services.ValidationService;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Autowired;

public class OtpValidator implements ConstraintValidator<ValidOtp, String> {
    
    @Autowired
    private ValidationService validationService;
    
    @Override
    public void initialize(ValidOtp constraintAnnotation) {
        // No initialization needed
    }
    
    @Override
    public boolean isValid(String otp, ConstraintValidatorContext context) {
        if (otp == null || otp.trim().isEmpty()) {
            return false;
        }
        
        return validationService != null && validationService.isValidOtpCode(otp);
    }
}
