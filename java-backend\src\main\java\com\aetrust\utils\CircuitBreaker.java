package com.aetrust.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import java.util.List;
import java.util.ArrayList;

@Slf4j
@Component
public class CircuitBreaker {
    
    private static final ConcurrentHashMap<String, CircuitBreakerInstance> instances = new ConcurrentHashMap<>();
    
    public enum CircuitState {
        CLOSED, OPEN, HALF_OPEN
    }
    
    public static class CircuitBreakerConfig {
        private int failureThreshold = 5;
        private long resetTimeout = 60000; // 1 minute
        private long monitoringPeriod = 10000; // 10 seconds
        private List<String> expectedErrors = new ArrayList<>();
        
        public CircuitBreakerConfig failureThreshold(int threshold) {
            this.failureThreshold = threshold;
            return this;
        }
        
        public CircuitBreakerConfig resetTimeout(long timeout) {
            this.resetTimeout = timeout;
            return this;
        }
        
        public CircuitBreakerConfig monitoringPeriod(long period) {
            this.monitoringPeriod = period;
            return this;
        }
        
        public CircuitBreakerConfig expectedErrors(List<String> errors) {
            this.expectedErrors = errors;
            return this;
        }
        
        // getters
        public int getFailureThreshold() { return failureThreshold; }
        public long getResetTimeout() { return resetTimeout; }
        public long getMonitoringPeriod() { return monitoringPeriod; }
        public List<String> getExpectedErrors() { return expectedErrors; }
    }
    
    private static class CircuitBreakerInstance {
        private final String name;
        private final CircuitBreakerConfig config;
        private volatile CircuitState state = CircuitState.CLOSED;
        private final AtomicInteger failureCount = new AtomicInteger(0);
        private final AtomicLong lastFailureTime = new AtomicLong(0);
        private final AtomicInteger successCount = new AtomicInteger(0);
        
        public CircuitBreakerInstance(String name, CircuitBreakerConfig config) {
            this.name = name;
            this.config = config;
        }
        
        public <T> T execute(Supplier<T> operation) throws Exception {
            if (state == CircuitState.OPEN) {
                if (shouldAttemptReset()) {
                    state = CircuitState.HALF_OPEN;
                    log.info("circuit breaker half-open: {}", name);
                } else {
                    throw new RuntimeException("circuit breaker open: " + name);
                }
            }
            
            try {
                T result = operation.get();
                onSuccess();
                return result;
            } catch (Exception error) {
                onFailure(error);
                throw error;
            }
        }
        
        private void onSuccess() {
            failureCount.set(0);
            
            if (state == CircuitState.HALF_OPEN) {
                int currentSuccessCount = successCount.incrementAndGet();
                if (currentSuccessCount >= 3) { // require 3 successes to close
                    state = CircuitState.CLOSED;
                    successCount.set(0);
                    log.info("circuit breaker closed: {}", name);
                }
            }
        }
        
        private void onFailure(Exception error) {

            if (config.getExpectedErrors().stream().anyMatch(expectedError -> 
                error.getMessage().contains(expectedError))) {
                return;
            }
            
            int currentFailureCount = failureCount.incrementAndGet();
            lastFailureTime.set(System.currentTimeMillis());
            
            if (state == CircuitState.HALF_OPEN) {
                state = CircuitState.OPEN;
                successCount.set(0);
                log.warn("circuit breaker opened from half-open: {} - error: {}", name, error.getMessage());
            } else if (currentFailureCount >= config.getFailureThreshold()) {
                state = CircuitState.OPEN;
                log.warn("circuit breaker opened: {} - failureCount: {} - error: {}", 
                    name, currentFailureCount, error.getMessage());
            }
        }
        
        private boolean shouldAttemptReset() {
            return System.currentTimeMillis() - lastFailureTime.get() >= config.getResetTimeout();
        }
        
        public CircuitState getState() {
            return state;
        }
        
        public int getFailureCount() {
            return failureCount.get();
        }
        
        public long getLastFailureTime() {
            return lastFailureTime.get();
        }
    }
    
    
    public static CircuitBreakerInstance getInstance(String name, CircuitBreakerConfig config) {
        return instances.computeIfAbsent(name, k -> new CircuitBreakerInstance(name, config));
    }
    
    
    public static <T> T execute(String name, CircuitBreakerConfig config, Supplier<T> operation) throws Exception {
        CircuitBreakerInstance instance = getInstance(name, config);
        return instance.execute(operation);
    }
    
   
    public static <T> T execute(String name, Supplier<T> operation) throws Exception {
        CircuitBreakerConfig defaultConfig = new CircuitBreakerConfig();
        return execute(name, defaultConfig, operation);
    }
    
    
    public static CircuitState getState(String name) {
        CircuitBreakerInstance instance = instances.get(name);
        return instance != null ? instance.getState() : CircuitState.CLOSED;
    }
    
   
    public static int getFailureCount(String name) {
        CircuitBreakerInstance instance = instances.get(name);
        return instance != null ? instance.getFailureCount() : 0;
    }
    
    
    public static void reset(String name) {
        CircuitBreakerInstance instance = instances.get(name);
        if (instance != null) {
            instance.state = CircuitState.CLOSED;
            instance.failureCount.set(0);
            instance.successCount.set(0);
            instance.lastFailureTime.set(0);
            log.info("circuit breaker reset: {}", name);
        }
    }
    
   
    public static ConcurrentHashMap<String, CircuitBreakerStats> getAllStats() {
        ConcurrentHashMap<String, CircuitBreakerStats> stats = new ConcurrentHashMap<>();
        
        instances.forEach((name, instance) -> {
            stats.put(name, new CircuitBreakerStats(
                name,
                instance.getState(),
                instance.getFailureCount(),
                instance.getLastFailureTime()
            ));
        });
        
        return stats;
    }
    
    public static class CircuitBreakerStats {
        private final String name;
        private final CircuitState state;
        private final int failureCount;
        private final long lastFailureTime;
        
        public CircuitBreakerStats(String name, CircuitState state, int failureCount, long lastFailureTime) {
            this.name = name;
            this.state = state;
            this.failureCount = failureCount;
            this.lastFailureTime = lastFailureTime;
        }
        
        // getters
        public String getName() { return name; }
        public CircuitState getState() { return state; }
        public int getFailureCount() { return failureCount; }
        public long getLastFailureTime() { return lastFailureTime; }
    }
}
