package com.aetrust.controllers;

import com.aetrust.dto.ApiResponse;
import com.aetrust.services.FileUploadService;
import com.aetrust.utils.CryptoUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/upload")
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private CryptoUtils cryptoUtils;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Base64UploadRequest {
        @NotBlank(message = "file data is required")
        private String fileData;

        @NotBlank(message = "upload type is required")
        private String uploadType;

        @NotBlank(message = "user ID is required")
        private String userId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MultipleBase64UploadRequest {
        @NotNull(message = "files are required")
        private Map<String, String> files;

        @NotBlank(message = "upload type is required")
        private String uploadType;

        @NotBlank(message = "user ID is required")
        private String userId;
    }

    /**
     * Upload a single base64 encoded file
     */
    @PostMapping("/base64")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadBase64File(
            @Valid @RequestBody Base64UploadRequest request) {
        try {
            UUID userId = UUID.fromString(request.getUserId());
            FileUploadService.UploadType uploadType = FileUploadService.UploadType.valueOf(request.getUploadType().toUpperCase());

            FileUploadService.UploadResult result = fileUploadService.uploadBase64File(
                request.getFileData(), 
                uploadType, 
                userId
            );

            if (result.isSuccess()) {
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("fileUrl", result.getFileUrl());
                responseData.put("fileName", result.getFileName());
                responseData.put("fileSize", result.getFileSize());
                responseData.put("publicId", result.getPublicId());
                responseData.put("resourceType", result.getResourceType());
                responseData.put("format", result.getFormat());

                log.info("File uploaded successfully for user: {}", cryptoUtils.maskSensitiveData(userId.toString(), 8));

                return ResponseEntity.ok(ApiResponse.success(
                    "File uploaded successfully",
                    responseData
                ));
            } else {
                log.warn("File upload failed for user: {}, error: {}", userId, result.getMessage());
                return ResponseEntity.badRequest().body(ApiResponse.error(
                    result.getMessage(),
                    result.getErrorCode()
                ));
            }

        } catch (IllegalArgumentException e) {
            log.warn("Invalid upload type or user ID: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(
                "Invalid upload type or user ID",
                "INVALID_PARAMETER"
            ));
        } catch (Exception e) {
            log.error("Error uploading file: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(ApiResponse.error(
                "Upload failed",
                "INTERNAL_ERROR"
            ));
        }
    }

    /**
     * Upload multiple base64 encoded files
     */
    @PostMapping("/base64/multiple")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadMultipleBase64Files(
            @Valid @RequestBody MultipleBase64UploadRequest request) {
        try {
            UUID userId = UUID.fromString(request.getUserId());
            FileUploadService.UploadType uploadType = FileUploadService.UploadType.valueOf(request.getUploadType().toUpperCase());

            List<FileUploadService.UploadResult> results = fileUploadService.uploadMultipleBase64Files(
                request.getFiles(), 
                uploadType, 
                userId
            );

            Map<String, Object> responseData = new HashMap<>();
            List<Map<String, Object>> uploadedFiles = new ArrayList<>();
            List<Map<String, Object>> failedFiles = new ArrayList<>();

            for (int i = 0; i < results.size(); i++) {
                FileUploadService.UploadResult result = results.get(i);
                Map<String, Object> fileData = new HashMap<>();

                if (result.isSuccess()) {
                    fileData.put("fileUrl", result.getFileUrl());
                    fileData.put("fileName", result.getFileName());
                    fileData.put("fileSize", result.getFileSize());
                    fileData.put("publicId", result.getPublicId());
                    fileData.put("resourceType", result.getResourceType());
                    fileData.put("format", result.getFormat());
                    uploadedFiles.add(fileData);
                } else {
                    fileData.put("error", result.getMessage());
                    fileData.put("errorCode", result.getErrorCode());
                    failedFiles.add(fileData);
                }
            }

            responseData.put("uploadedFiles", uploadedFiles);
            responseData.put("failedFiles", failedFiles);
            responseData.put("totalFiles", results.size());
            responseData.put("successCount", uploadedFiles.size());
            responseData.put("failureCount", failedFiles.size());

            log.info("Multiple files upload completed for user: {}, success: {}, failed: {}", 
                cryptoUtils.maskSensitiveData(userId.toString(), 8), uploadedFiles.size(), failedFiles.size());

            return ResponseEntity.ok(ApiResponse.success(
                String.format("Upload completed: %d successful, %d failed", uploadedFiles.size(), failedFiles.size()),
                responseData
            ));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid upload type or user ID: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(
                "Invalid upload type or user ID",
                "INVALID_PARAMETER"
            ));
        } catch (Exception e) {
            log.error("Error uploading multiple files: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(ApiResponse.error(
                "Upload failed",
                "INTERNAL_ERROR"
            ));
        }
    }

    /**
     * Upload a single multipart file (traditional upload)
     */
    @PostMapping("/multipart")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadMultipartFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("uploadType") String uploadType,
            @RequestParam("userId") String userId) {
        try {
            UUID userUuid = UUID.fromString(userId);

            FileUploadService.UploadResult result = fileUploadService.uploadProfilePicture(file, userUuid);

            if (result.isSuccess()) {
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("fileUrl", result.getFileUrl());
                responseData.put("fileName", result.getFileName());
                responseData.put("fileSize", result.getFileSize());

                log.info("Multipart file uploaded successfully for user: {}", cryptoUtils.maskSensitiveData(userId, 8));

                return ResponseEntity.ok(ApiResponse.success(
                    "File uploaded successfully",
                    responseData
                ));
            } else {
                log.warn("Multipart file upload failed for user: {}, error: {}", userId, result.getMessage());
                return ResponseEntity.badRequest().body(ApiResponse.error(
                    result.getMessage(),
                    result.getErrorCode()
                ));
            }

        } catch (IllegalArgumentException e) {
            log.warn("Invalid user ID: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(
                "Invalid user ID",
                "INVALID_PARAMETER"
            ));
        } catch (Exception e) {
            log.error("Error uploading multipart file: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(ApiResponse.error(
                "Upload failed",
                "INTERNAL_ERROR"
            ));
        }
    }

    /**
     * Get upload configuration and validation info
     */
    @GetMapping("/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUploadConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // Upload types
            List<String> uploadTypes = new ArrayList<>();
            for (FileUploadService.UploadType type : FileUploadService.UploadType.values()) {
                uploadTypes.add(type.getValue());
            }
            config.put("uploadTypes", uploadTypes);
            
            // Configuration info
            config.put("cloudinaryEnabled", fileUploadService.validateCloudinaryConfig());
            config.put("maxFileSize", "10MB");
            config.put("allowedFormats", Arrays.asList("jpg", "jpeg", "png", "pdf"));
            config.put("supportedMimeTypes", Arrays.asList(
                "image/jpeg", "image/jpg", "image/png", "application/pdf"
            ));

            return ResponseEntity.ok(ApiResponse.success(
                "Upload configuration retrieved successfully",
                config
            ));

        } catch (Exception e) {
            log.error("Error retrieving upload config: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(ApiResponse.error(
                "Failed to retrieve configuration",
                "INTERNAL_ERROR"
            ));
        }
    }
}
