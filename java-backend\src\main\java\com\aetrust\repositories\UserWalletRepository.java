package com.aetrust.repositories;

import com.aetrust.models.User;
import com.aetrust.types.Types.WalletType;
import com.aetrust.types.Types.WalletStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserWalletRepository extends JpaRepository<User.UserWallet, Long> {

    List<User.UserWallet> findByUserIdAndDeletedAtIsNull(Long userId);
    List<User.UserWallet> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

    Optional<User.UserWallet> findByUserIdAndIsDefaultTrueAndDeletedAtIsNull(Long userId);
    Optional<User.UserWallet> findByUserUuidAndIsDefaultTrueAndDeletedAtIsNull(UUID userUuid);

    Optional<User.UserWallet> findByUserIdAndWalletTypeAndCurrencyAndDeletedAtIsNull(
        Long userId, WalletType walletType, String currency);
    Optional<User.UserWallet> findByUserUuidAndWalletTypeAndCurrencyAndDeletedAtIsNull(
        UUID userUuid, WalletType walletType, String currency);

    List<User.UserWallet> findByUserIdAndStatusAndDeletedAtIsNull(Long userId, WalletStatus status);
    List<User.UserWallet> findByStatusAndDeletedAtIsNull(WalletStatus status);

    Optional<User.UserWallet> findByWalletAddressAndDeletedAtIsNull(String walletAddress);

    @Query("SELECT w FROM User$UserWallet w WHERE w.userId = :userId AND w.balance >= :minBalance AND w.deletedAt IS NULL")
    List<User.UserWallet> findByUserIdAndBalanceGreaterThanEqual(@Param("userId") Long userId, @Param("minBalance") BigDecimal minBalance);

    @Query("SELECT w FROM User$UserWallet w WHERE w.balance >= :minBalance AND w.deletedAt IS NULL")
    List<User.UserWallet> findByBalanceGreaterThanEqual(@Param("minBalance") BigDecimal minBalance);

    @Query("SELECT SUM(w.balance) FROM User$UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
    BigDecimal getTotalBalanceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);

    @Query("SELECT SUM(w.availableBalance) FROM User$UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
    BigDecimal getTotalAvailableBalanceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);

    @Query("SELECT w FROM User$UserWallet w WHERE w.totalTransactions >= :minTransactions AND w.deletedAt IS NULL")
    List<User.UserWallet> findByTotalTransactionsGreaterThanEqual(@Param("minTransactions") Integer minTransactions);

    @Query("SELECT w FROM User$UserWallet w WHERE w.lastTransactionDate >= :date AND w.deletedAt IS NULL")
    List<User.UserWallet> findByLastTransactionDateAfter(@Param("date") LocalDateTime date);

    @Query("SELECT w.currency, COUNT(w) FROM User$UserWallet w WHERE w.deletedAt IS NULL GROUP BY w.currency")
    List<Object[]> getWalletCountByCurrency();

    @Query("SELECT w.currency, SUM(w.balance) FROM User$UserWallet w WHERE w.status = :status AND w.deletedAt IS NULL GROUP BY w.currency")
    List<Object[]> getTotalBalanceByCurrency(@Param("status") WalletStatus status);

    boolean existsByUserIdAndWalletTypeAndDeletedAtIsNull(Long userId, WalletType walletType);
    boolean existsByUserUuidAndWalletTypeAndDeletedAtIsNull(UUID userUuid, WalletType walletType);
    boolean existsByWalletAddressAndDeletedAtIsNull(String walletAddress);

    @Query("SELECT COUNT(w) FROM User$UserWallet w WHERE w.userId = :userId AND w.status = :status AND w.deletedAt IS NULL")
    long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") WalletStatus status);

    @Modifying
    @Query("UPDATE User$UserWallet w SET w.deletedAt = :deletedAt WHERE w.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserWallet w SET w.deletedAt = :deletedAt WHERE w.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserWallet w SET w.balance = w.balance + :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId")
    void creditWallet(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    @Modifying
    @Query("UPDATE User$UserWallet w SET w.balance = w.balance - :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId")
    void debitWallet(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    @Modifying
    @Query("UPDATE User$UserWallet w SET w.availableBalance = w.availableBalance - :amount, w.pendingBalance = w.pendingBalance + :amount WHERE w.id = :walletId")
    void freezeAmount(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    @Modifying
    @Query("UPDATE User$UserWallet w SET w.availableBalance = w.availableBalance + :amount, w.pendingBalance = w.pendingBalance - :amount WHERE w.id = :walletId")
    void unfreezeAmount(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);
}
