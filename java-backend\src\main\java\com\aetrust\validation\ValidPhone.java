package com.aetrust.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = PhoneValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidPhone {
    String message() default "Phone number must be in international format (e.g., +250788123456)";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
