import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { SecurityMiddleware } from '../middleware/security.middleware';
import * as secureTransferController from '../controllers/secure-transfer.controller';

export async function secureTransferRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // all routes require authentication
  fastify.addHook('preHandler', AuthMiddleware.authenticateRequest);
  fastify.addHook('preHandler', SecurityMiddleware.requestId);

  // secure money transfer
  fastify.post('/send', {
    preHandler: [
      SecurityMiddleware.transactionSecurity,
      SecurityMiddleware.securityCheck
    ]
  }, secureTransferController.secureSendMoney as any);

  // transfer validation
  fastify.post('/validate', {
    preHandler: [
      SecurityMiddleware.rateLimiting
    ]
  }, secureTransferController.secureValidateTransfer as any);

  // wallet balance
  fastify.get('/wallet/:walletId/balance', {
    preHandler: [
      SecurityMiddleware.rateLimiting
    ]
  }, secureTransferController.secureGetWalletBalance as any);

  // health check
  fastify.get('/health', secureTransferController.secureWalletHealth as any);
}
