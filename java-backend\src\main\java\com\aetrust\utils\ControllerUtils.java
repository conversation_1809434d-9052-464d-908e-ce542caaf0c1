package com.aetrust.utils;

import com.aetrust.dto.ApiResponse;
import com.aetrust.dto.RequestDTOs.BaseRequest;
import com.aetrust.services.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.HashMap;


@Slf4j
@Component
public class ControllerUtils {

    @Autowired
    private SecurityService securityService;

  
    public boolean isEmptyRequest(Object request) {
        if (request == null) {
            return true;
        }
        
        try {
            java.lang.reflect.Field[] fields = request.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(request);
                if (value != null) {
                    if (value instanceof String && !((String) value).trim().isEmpty()) {
                        return false;
                    } else if (!(value instanceof String) && value != null) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.warn("Error checking empty request: {}", e.getMessage());
            return false;
        }
    }

   
    public boolean isEmptyParam(String param) {
        return param == null || param.trim().isEmpty();
    }

   
    public boolean isEmptyBody(String rawBody) {
        return rawBody == null || rawBody.trim().isEmpty();
    }

 
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleEmptyRequest(String operation) {
        log.warn("Empty request body received for operation: {}", operation);
        return ResponseEntity.badRequest().body(
            ApiResponse.error("Request body cannot be empty", "EMPTY_REQUEST"));
    }

  
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleEmptyParam(String paramName) {
        log.warn("Empty parameter received: {}", paramName);
        return ResponseEntity.badRequest().body(
            ApiResponse.error("Parameter '" + paramName + "' cannot be empty", "EMPTY_PARAMETER"));
    }


    public ResponseEntity<ApiResponse<Map<String, Object>>> handleRateLimitExceeded(String message) {
        return ResponseEntity.status(429).body(
            ApiResponse.error(message, "RATE_LIMIT_EXCEEDED"));
    }

  
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleInvalidInput(String message) {
        return ResponseEntity.badRequest().body(
            ApiResponse.error(message, "INVALID_INPUT"));
    }

    
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleInternalError(String operation) {
        log.error("Internal error in operation: {}", operation);
        return ResponseEntity.status(500).body(
            ApiResponse.error(operation + " failed", "INTERNAL_ERROR"));
    }

   
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleJsonError(String ipAddress) {
        log.warn("Invalid JSON format from IP: {}", ipAddress);
        return ResponseEntity.badRequest().body(
            ApiResponse.error("Invalid JSON format", "INVALID_JSON"));
    }

  
    public String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

   
    public void setRequestMetadata(BaseRequest request, HttpServletRequest httpRequest) {
        if (request != null) {
            request.setIpAddress(getClientIp(httpRequest));
            request.setUserAgent(httpRequest.getHeader("User-Agent"));
        }
    }

 
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkRateLimit(
            String identifier, String type, String endpoint) {
        SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(identifier, type, endpoint);
        
        if (!rateLimit.isAllowed()) {
            return handleRateLimitExceeded("Too many attempts");
        }
        
        return null; // No rate limit exceeded
    }

 
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkRateLimit(
            String identifier, String type, String endpoint, String message) {
        SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(identifier, type, endpoint);
        
        if (!rateLimit.isAllowed()) {
            return handleRateLimitExceeded(message);
        }
        
        return null; // No rate limit exceeded
    }

 
    public ResponseEntity<ApiResponse<Map<String, Object>>> createSuccessResponse(
            String message, Map<String, Object> data) {
        return ResponseEntity.ok(ApiResponse.success(message, data));
    }

  
    public ResponseEntity<ApiResponse<Map<String, Object>>> createSuccessResponse(String message) {
        return ResponseEntity.ok(ApiResponse.success(message, new HashMap<>()));
    }


    public boolean areFieldsNotEmpty(String... fields) {
        for (String field : fields) {
            if (isEmptyParam(field)) {
                return false;
            }
        }
        return true;
    }

 
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleMissingFields(String... fieldNames) {
        String fields = String.join(", ", fieldNames);
        log.warn("Missing required fields: {}", fields);
        return ResponseEntity.badRequest().body(
            ApiResponse.error(fields + " are required", "MISSING_REQUIRED_FIELDS"));
    }
}
