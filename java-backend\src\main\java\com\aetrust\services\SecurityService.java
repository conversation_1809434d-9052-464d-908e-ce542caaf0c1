package com.aetrust.services;

import com.aetrust.models.SystemConfig;
import com.aetrust.utils.CryptoUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.time.LocalTime;
import java.time.Instant;
import java.time.ZoneId;
import java.net.InetAddress;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Slf4j
@Service
public class SecurityService {

    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final long LOCKOUT_DURATION = 900000; // 15 minutes
    private static final int IP_RATE_LIMIT = 100;
    private static final int USER_RATE_LIMIT = 50;
    private static final long RATE_LIMIT_WINDOW = 3600000; // 1 hour

    // Global cache service - auto-switches between Redis and in-memory
    @Autowired
    private CacheService cacheService;

    @Autowired
    private CryptoUtils cryptoUtils;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${aetrust.security.threat-intelligence.enabled:true}")
    private boolean threatIntelEnabled;

    @Value("${aetrust.security.device-fingerprinting.enabled:true}")
    private boolean deviceFingerprintingEnabled;

    @Value("${aetrust.security.geo-blocking.enabled:true}")
    private boolean geoBlockingEnabled;

    @Value("${aetrust.security.behavioral-analysis.enabled:true}")
    private boolean behavioralAnalysisEnabled;
    
    /**
     * Record failed login attempt with progressive penalties
     */
    public void recordFailedAttempt(String identifier, String type) {
        try {
            String key = "bruteforce:" + type + ":" + identifier;
            String attemptsStr = cacheService.get(key);
            int attemptCount = (attemptsStr != null ? Integer.parseInt(attemptsStr) : 0) + 1;

            int maxAttempts = systemConfigService.getIntConfig("securityMaxLoginAttempts", MAX_LOGIN_ATTEMPTS);
            long lockoutDuration = systemConfigService.getLongConfig("securityLockoutDuration", LOCKOUT_DURATION);

            // set attempt count with exponential backoff
            long ttl = Math.min(3600, 60 * (long) Math.pow(2, attemptCount - 1)); // max 1 hour
            cacheService.set(key, String.valueOf(attemptCount), ttl, TimeUnit.SECONDS);

            if (attemptCount >= maxAttempts) {
                String lockKey = "locked:" + type + ":" + identifier;
                cacheService.set(lockKey, "true", lockoutDuration, TimeUnit.MILLISECONDS);

                log.warn("Account locked due to failed attempts - type: {}, identifier: {}, attempts: {}",
                    type, maskSensitiveData(identifier), attemptCount);
            }

        } catch (Exception error) {
            log.error("Error recording failed attempt: {}", error.getMessage());
        }
    }
    
    /**
     * Check brute force protection
     */
    public BruteForceResult checkBruteForceProtection(String identifier, String type) {
        try {
            String lockKey = "locked:" + type + ":" + identifier;
            String isLocked = cacheService.get(lockKey);

            if ("true".equals(isLocked)) {
                // For cache service, we'll check if key exists (TTL not directly available)
                boolean keyExists = cacheService.exists(lockKey);
                return BruteForceResult.builder()
                    .allowed(false)
                    .lockoutTime(keyExists ? 300000L : 0L) // Default 5 minutes if locked
                    .reason("Account temporarily locked due to multiple failed attempts")
                    .build();
            }
            
            return BruteForceResult.builder()
                .allowed(true)
                .build();
                
        } catch (Exception error) {
            log.error("Error checking brute force protection: {}", error.getMessage());
            return BruteForceResult.builder().allowed(true).build(); // fail open
        }
    }
    
    /**
     * Clear failed attempts on successful authentication
     */
    public void clearFailedAttempts(String identifier, String type) {
        try {
            String key = "bruteforce:" + type + ":" + identifier;
            String lockKey = "locked:" + type + ":" + identifier;
            
            cacheService.delete(key);
            cacheService.delete(lockKey);
            
        } catch (Exception error) {
            log.error("Error clearing failed attempts: {}", error.getMessage());
        }
    }
    
    /**
     * Check rate limiting
     */
    public RateLimitResult checkRateLimit(String identifier, String type, String endpoint) {
        try {
            int limit = type.equals("ip") ? 
                systemConfigService.getIntConfig("securityIpRateLimit", IP_RATE_LIMIT) :
                systemConfigService.getIntConfig("securityUserRateLimit", USER_RATE_LIMIT);
            
            long window = systemConfigService.getLongConfig("securityRateLimitWindow", RATE_LIMIT_WINDOW);
            
            String baseKey = "ratelimit:" + type + ":" + identifier;
            if (endpoint != null) {
                baseKey += ":" + endpoint;
            }
            
            String currentStr = cacheService.get(baseKey);
            int count = currentStr != null ? Integer.parseInt(currentStr) : 0;

            if (count >= limit) {
                // For cache service, estimate remaining time
                long ttl = cacheService.exists(baseKey) ? (window / 1000) : 0;
                
                log.warn("Rate limit exceeded - type: {}, identifier: {}, endpoint: {}, count: {}, limit: {}", 
                    type, maskSensitiveData(identifier), endpoint, count, limit);
                
                return RateLimitResult.builder()
                    .allowed(false)
                    .remaining(0)
                    .resetTime(ttl)
                    .reason("Rate limit exceeded. Please try again later.")
                    .build();
            }
            
            // increment counter
            if (currentStr != null) {
                Long newCount = cacheService.increment(baseKey);
            } else {
                cacheService.set(baseKey, "1", window / 1000, TimeUnit.SECONDS);
            }
            
            return RateLimitResult.builder()
                .allowed(true)
                .remaining(limit - count - 1)
                .resetTime(window / 1000)
                .build();
                
        } catch (Exception error) {
            log.error("Error checking rate limit: {}", error.getMessage());
            return RateLimitResult.builder().allowed(true).remaining(1000).resetTime(3600L).build(); // fail open
        }
    }
    
    /**
     * Detect suspicious activity with comprehensive threat analysis
     */
    public SuspiciousActivityResult detectSuspiciousActivity(String userId, ActivityContext activity) {
        try {
            int riskScore = 0;
            List<String> reasons = new ArrayList<>();

            if (behavioralAnalysisEnabled) {
                if (isAnomalousLoginPattern(userId, activity)) {
                    riskScore += 25;
                    reasons.add("anomalous_login_pattern");
                }

                if (hasVelocityAnomaly(userId, activity)) {
                    riskScore += 30;
                    reasons.add("velocity_anomaly");
                }
            }

            if (deviceFingerprintingEnabled) {
                String deviceFingerprint = generateDeviceFingerprint(activity);
                if (isNewOrSuspiciousDevice(userId, deviceFingerprint)) {
                    riskScore += 35;
                    reasons.add("suspicious_device");
                }
            }

            if (threatIntelEnabled) {
                ThreatIntelResult threatResult = checkThreatIntelligence(activity.getIpAddress());
                if (threatResult.isThreat()) {
                    riskScore += threatResult.getThreatLevel();
                    reasons.add("threat_intel_match");
                }
            }

            if (geoBlockingEnabled) {
                GeoLocationResult geoResult = analyzeGeolocation(userId, activity.getIpAddress());
                if (geoResult.isSuspicious()) {
                    riskScore += geoResult.getRiskScore();
                    reasons.add("suspicious_location");
                }
            }

            if (hasBehavioralAnomalies(userId, activity)) {
                riskScore += 20;
                reasons.add("behavioral_anomaly");
            }

            String action = determineSecurityAction(riskScore, userId);

            if (riskScore > 0) {
                logSecurityEvent(userId, activity, riskScore, reasons, action);
            }

            return SuspiciousActivityResult.builder()
                .isSuspicious(riskScore > 0)
                .riskScore(riskScore)
                .reasons(reasons)
                .action(action)
                .build();

        } catch (Exception error) {
            log.error("Error detecting suspicious activity: {}", error.getMessage());
            return SuspiciousActivityResult.builder()
                .isSuspicious(false)
                .riskScore(0)
                .reasons(new ArrayList<>())
                .action("allow")
                .build();
        }
    }
    
    /**
     * Validate session integrity with comprehensive security checks
     */
    public SessionValidationResult validateSessionIntegrity(String userId, SessionContext context) {
        try {
            String sessionKey = "session:" + userId;
            String sessionDataStr = cacheService.get(sessionKey);

            if (sessionDataStr == null) {
                // create new session with full security context
                SessionData sessionData = new SessionData();
                sessionData.setIpAddress(context.getIpAddress());
                sessionData.setUserAgent(context.getUserAgent());
                sessionData.setLastActivity(System.currentTimeMillis());
                sessionData.setTokenIssuedAt(context.getTokenIssuedAt());
                sessionData.setDeviceFingerprint(generateDeviceFingerprint(
                    new ActivityContext(context.getIpAddress(), context.getUserAgent(), System.currentTimeMillis())
                ));
                sessionData.setCreatedAt(System.currentTimeMillis());
                sessionData.setSecurityFlags(initializeSecurityFlags());

                String sessionJson = objectMapper.writeValueAsString(sessionData);
                cacheService.set(sessionKey, sessionJson, 3600, TimeUnit.SECONDS);

                return SessionValidationResult.builder()
                    .isValid(true)
                    .action("continue")
                    .build();
            }

            // validate existing session with security checks
            SessionData existingSession = objectMapper.readValue(sessionDataStr, SessionData.class);

            // check for session hijacking indicators
            if (!validateSessionConsistency(existingSession, context)) {
                invalidateSession(userId);
                return SessionValidationResult.builder()
                    .isValid(false)
                    .action("terminate")
                    .build();
            }

            // check for concurrent sessions
            if (detectConcurrentSessions(userId, existingSession)) {
                return SessionValidationResult.builder()
                    .isValid(false)
                    .action("challenge")
                    .build();
            }

            // update session with new activity
            existingSession.setLastActivity(System.currentTimeMillis());
            existingSession.setIpAddress(context.getIpAddress());
            existingSession.updateSecurityMetrics();

            String updatedSessionJson = objectMapper.writeValueAsString(existingSession);
            cacheService.set(sessionKey, updatedSessionJson, 3600, TimeUnit.SECONDS);

            return SessionValidationResult.builder()
                .isValid(true)
                .action("continue")
                .build();

        } catch (Exception error) {
            log.error("Error validating session integrity: {}", error.getMessage());
            return SessionValidationResult.builder().isValid(false).action("terminate").build();
        }
    }
    
    
    public String maskSensitiveData(String data) {
        return cryptoUtils.maskSensitiveData(data, 2);
    }
    
    private boolean isAnomalousLoginPattern(String userId, ActivityContext activity) {
        try {
            String patternKey = "login_pattern:" + userId;
            String patternData = cacheService.get(patternKey);

            if (patternData == null) {
                // pattern tracking
                LoginPattern pattern = new LoginPattern();
                pattern.addLoginTime(activity.getTimestamp());
                cacheService.set(patternKey, objectMapper.writeValueAsString(pattern), 30, TimeUnit.DAYS);
                return false;
            }

            LoginPattern existingPattern = objectMapper.readValue(patternData, LoginPattern.class);
            boolean isAnomalous = existingPattern.isAnomalous(activity.getTimestamp());
            existingPattern.addLoginTime(activity.getTimestamp());

            cacheService.set(patternKey, objectMapper.writeValueAsString(existingPattern), 30, TimeUnit.DAYS);
            return isAnomalous;

        } catch (Exception e) {
            log.error("Error analyzing login pattern: {}", e.getMessage());
            return false;
        }
    }

    private boolean hasVelocityAnomaly(String userId, ActivityContext activity) {
        try {
            String velocityKey = "velocity:" + userId;
            Long lastActivity = null;

            if (lastActivity == null) {
                cacheService.set(velocityKey, String.valueOf(activity.getTimestamp()), 5, TimeUnit.MINUTES);
                return false;
            }

            long timeDiff = activity.getTimestamp() - Long.parseLong(cacheService.get(velocityKey));
            cacheService.set(velocityKey, String.valueOf(activity.getTimestamp()), 5, TimeUnit.MINUTES);

            // flag if activities are too close together (less than 30 seconds)
            return timeDiff < 30000;

        } catch (Exception e) {
            log.error("Error checking velocity anomaly: {}", e.getMessage());
            return false;
        }
    }

    private String generateDeviceFingerprint(ActivityContext activity) {
        try {
            StringBuilder fingerprint = new StringBuilder();
            fingerprint.append(activity.getUserAgent());
            fingerprint.append("|");
            fingerprint.append(activity.getIpAddress());
            fingerprint.append("|");
            fingerprint.append(System.getProperty("user.timezone", "UTC"));

            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(fingerprint.toString().getBytes(StandardCharsets.UTF_8));

            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (Exception e) {
            log.error("Error generating device fingerprint: {}", e.getMessage());
            return "unknown";
        }
    }

    private boolean isNewOrSuspiciousDevice(String userId, String deviceFingerprint) {
        try {
            String deviceKey = "known_devices:" + userId;
            Set<String> knownDevices = parseStringSet(cacheService.get(deviceKey));

            if (knownDevices == null || knownDevices.isEmpty()) {
                cacheService.set(deviceKey, serializeStringSet(deviceFingerprint));
                cacheService.expire(deviceKey, 90, TimeUnit.DAYS);
                return true; // new user, first device
            }

            if (!knownDevices.contains(deviceFingerprint)) {
                // check if we should add this device or flag as suspicious
                if (knownDevices.size() >= 5) { // max 5 devices per user
                    return true; // suspicious
                }

                cacheService.set(deviceKey, serializeStringSet(deviceFingerprint));
                return true; // new device
            }

            return false; // known device

        } catch (Exception e) {
            log.error("Error checking device fingerprint: {}", e.getMessage());
            return false;
        }
    }

    private ThreatIntelResult checkThreatIntelligence(String ipAddress) {
        try {
            // check against known threat databases
            String threatKey = "threat_intel:" + ipAddress;
            String threatData = cacheService.get(threatKey);

            if (threatData != null) {
                ThreatIntelResult cached = objectMapper.readValue(threatData, ThreatIntelResult.class);
                return cached;
            }

            // real threat intelligence checks would go here
            // for now, implement basic checks
            ThreatIntelResult result = performThreatAnalysis(ipAddress);

            // cache result for 1 hour
            cacheService.set(threatKey, objectMapper.writeValueAsString(result), 1, TimeUnit.HOURS);

            return result;

        } catch (Exception e) {
            log.error("Error checking threat intelligence: {}", e.getMessage());
            return new ThreatIntelResult(false, 0, "unknown");
        }
    }

    private ThreatIntelResult performThreatAnalysis(String ipAddress) {
        try {
            InetAddress addr = InetAddress.getByName(ipAddress);

            if (addr.isSiteLocalAddress() || addr.isLoopbackAddress()) {
                return new ThreatIntelResult(false, 0, "private");
            }

            if (isKnownMaliciousPattern(ipAddress)) {
                return new ThreatIntelResult(true, 50, "malicious_pattern");
            }

            if (isHighRiskGeolocation(ipAddress)) {
                return new ThreatIntelResult(true, 30, "high_risk_geo");
            }

            if (checkThreatDatabases(ipAddress)) {
                return new ThreatIntelResult(true, 60, "threat_database_match");
            }

            if (isSuspiciousNetworkRange(ipAddress)) {
                return new ThreatIntelResult(true, 25, "suspicious_network");
            }

            return new ThreatIntelResult(false, 0, "clean");

        } catch (Exception e) {
            log.error("Error performing threat analysis: {}", e.getMessage());
            return new ThreatIntelResult(false, 0, "error");
        }
    }

    private boolean isKnownMaliciousPattern(String ipAddress) {
        try {
            // check against known malicious IP ranges
            Set<String> maliciousRanges = Set.of(
                "185.220.", "198.98.", "199.87.", "162.247.", "107.189.",
                "192.42.116.", "171.25.", "185.165.", "23.129.", "104.244."
            );

            for (String range : maliciousRanges) {
                if (ipAddress.startsWith(range)) {
                    return true;
                }
            }

            return isTorExitNode(ipAddress) || isKnownProxyVpn(ipAddress);

        } catch (Exception e) {
            log.error("Error checking malicious patterns: {}", e.getMessage());
            return false;
        }
    }

    private boolean isHighRiskGeolocation(String ipAddress) {
        try {
            String country = getCountryFromIP(ipAddress);

            Set<String> highRiskCountries = Set.of(
                "CN", "RU", "IR", "KP", "BY", "MM", "AF", "IQ", "LY", "SO", "SY", "YE"
            );

            return highRiskCountries.contains(country);

        } catch (Exception e) {
            log.error("Error checking geolocation risk: {}", e.getMessage());
            return false;
        }
    }

    private boolean checkThreatDatabases(String ipAddress) {
        try {
            String threatKey = "threat_db:" + ipAddress;
            String cached = cacheService.get(threatKey);

            if (cached != null) {
                return Boolean.parseBoolean(cached);
            }

            // check multiple threat intelligence sources
            boolean isThreat = checkSpamhausDatabase(ipAddress) ||
                              checkAbuseIPDatabase(ipAddress) ||
                              checkVirusTotalDatabase(ipAddress);

            cacheService.set(threatKey, String.valueOf(isThreat), 6, TimeUnit.HOURS);
            return isThreat;

        } catch (Exception e) {
            log.error("Error checking threat databases: {}", e.getMessage());
            return false;
        }
    }

    private boolean isSuspiciousNetworkRange(String ipAddress) {
        try {
            // check for cloud/hosting providers that are commonly abused
            Set<String> suspiciousRanges = Set.of(
                "104.16.", "172.67.", "104.21.", "198.41.", // Cloudflare
                "52.", "54.", "3.", "13.", // AWS
                "35.", "34.", "104.196.", // Google Cloud
                "40.", "52.", "13.64.", // Azure
                "167.99.", "159.89.", "138.197." // DigitalOcean
            );

            for (String range : suspiciousRanges) {
                if (ipAddress.startsWith(range)) {
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("Error checking suspicious network ranges: {}", e.getMessage());
            return false;
        }
    }

    private GeoLocationResult analyzeGeolocation(String userId, String ipAddress) {
        try {
            String geoKey = "geo_history:" + userId;
            String geoData = cacheService.get(geoKey);

            GeoLocationHistory history;
            if (geoData == null) {
                history = new GeoLocationHistory();
            } else {
                history = objectMapper.readValue(geoData, GeoLocationHistory.class);
            }

            String currentLocation = getLocationFromIP(ipAddress);
            boolean isSuspicious = history.isLocationSuspicious(currentLocation);
            int riskScore = history.calculateLocationRisk(currentLocation);

            history.addLocation(currentLocation, System.currentTimeMillis());
            cacheService.set(geoKey, objectMapper.writeValueAsString(history), 30, TimeUnit.DAYS);

            return new GeoLocationResult(isSuspicious, riskScore, currentLocation);

        } catch (Exception e) {
            log.error("Error analyzing geolocation: {}", e.getMessage());
            return new GeoLocationResult(false, 0, "unknown");
        }
    }

    private String getLocationFromIP(String ipAddress) {
        try {
            String geoKey = "geo_ip:" + ipAddress;
            String cached = cacheService.get(geoKey);

            if (cached != null) {
                return cached;
            }

            // implement geolocation lookup using multiple sources
            String location = performGeoLookup(ipAddress);

            cacheService.set(geoKey, location, 24, TimeUnit.HOURS);
            return location;

        } catch (Exception e) {
            log.error("Error getting location from IP: {}", e.getMessage());
            return "unknown";
        }
    }

    private String getCountryFromIP(String ipAddress) {
        try {
            String countryKey = "country_ip:" + ipAddress;
            String cached = cacheService.get(countryKey);

            if (cached != null) {
                return cached;
            }

            String country = performCountryLookup(ipAddress);

            cacheService.set(countryKey, country, 24, TimeUnit.HOURS);
            return country;

        } catch (Exception e) {
            log.error("Error getting country from IP: {}", e.getMessage());
            return "unknown";
        }
    }

    private String performGeoLookup(String ipAddress) {
        // implement actual geolocation service integration
        // this would integrate with services like MaxMind, IPStack, etc.

        // for now, return basic classification
        if (ipAddress.startsWith("192.168.") || ipAddress.startsWith("10.") || ipAddress.startsWith("172.")) {
            return "private";
        }

        // basic country detection based on known ranges
        if (ipAddress.startsWith("1.") || ipAddress.startsWith("14.") || ipAddress.startsWith("27.")) {
            return "CN"; // China
        } else if (ipAddress.startsWith("5.") || ipAddress.startsWith("31.") || ipAddress.startsWith("46.")) {
            return "RU"; // Russia
        } else if (ipAddress.startsWith("8.8.") || ipAddress.startsWith("4.2.")) {
            return "US"; // United States
        }

        return "unknown";
    }

    private String performCountryLookup(String ipAddress) {
        // extract country from full geolocation
        String location = performGeoLookup(ipAddress);

        // if location is a country code, return it
        if (location.length() == 2) {
            return location;
        }

        return "unknown";
    }

    private boolean isTorExitNode(String ipAddress) {
        try {
            String torKey = "tor_exit:" + ipAddress;
            String cached = cacheService.get(torKey);

            if (cached != null) {
                return Boolean.parseBoolean(cached);
            }

            // check against tor exit node lists
            boolean isTor = checkTorExitNodeList(ipAddress);

            cacheService.set(torKey, String.valueOf(isTor), 12, TimeUnit.HOURS);
            return isTor;

        } catch (Exception e) {
            log.error("Error checking tor exit node: {}", e.getMessage());
            return false;
        }
    }

    private boolean isKnownProxyVpn(String ipAddress) {
        try {
            String proxyKey = "proxy_vpn:" + ipAddress;
            String cached = cacheService.get(proxyKey);

            if (cached != null) {
                return Boolean.parseBoolean(cached);
            }

            boolean isProxy = checkProxyVpnDatabase(ipAddress);

            cacheService.set(proxyKey, String.valueOf(isProxy), 6, TimeUnit.HOURS);
            return isProxy;

        } catch (Exception e) {
            log.error("Error checking proxy/VPN: {}", e.getMessage());
            return false;
        }
    }

    private boolean hasBehavioralAnomalies(String userId, ActivityContext activity) {
        try {
            String behaviorKey = "behavior:" + userId;
            String behaviorData = cacheService.get(behaviorKey);

            BehavioralProfile profile;
            if (behaviorData == null) {
                profile = new BehavioralProfile();
            } else {
                profile = objectMapper.readValue(behaviorData, BehavioralProfile.class);
            }

            boolean hasAnomalies = profile.detectAnomalies(activity);
            profile.updateProfile(activity);

            cacheService.set(behaviorKey, objectMapper.writeValueAsString(profile), 30, TimeUnit.DAYS);

            return hasAnomalies;

        } catch (Exception e) {
            log.error("Error checking behavioral anomalies: {}", e.getMessage());
            return false;
        }
    }

    private String determineSecurityAction(int riskScore, String userId) {
        int blockThreshold = systemConfigService.getIntConfig("securityBlockThreshold", 70);
        int challengeThreshold = systemConfigService.getIntConfig("securityChallengeThreshold", 40);

        // adjust thresholds based on user history
        UserRiskProfile userProfile = getUserRiskProfile(userId);
        if (userProfile.isHighRisk()) {
            blockThreshold -= 20;
            challengeThreshold -= 15;
        } else if (userProfile.isTrusted()) {
            blockThreshold += 10;
            challengeThreshold += 10;
        }

        if (riskScore >= blockThreshold) {
            return "block";
        } else if (riskScore >= challengeThreshold) {
            return "challenge";
        }

        return "allow";
    }

    private UserRiskProfile getUserRiskProfile(String userId) {
        try {
            String profileKey = "risk_profile:" + userId;
            String profileData = cacheService.get(profileKey);

            if (profileData == null) {
                return new UserRiskProfile(); // default profile
            }

            return objectMapper.readValue(profileData, UserRiskProfile.class);

        } catch (Exception e) {
            log.error("Error getting user risk profile: {}", e.getMessage());
            return new UserRiskProfile();
        }
    }

    // threat database check methods
    private boolean checkSpamhausDatabase(String ipAddress) {
        try {
            // implement Spamhaus SBL/CSS/PBL checks
            // this would typically involve DNS queries to Spamhaus
            return performDnsBlacklistCheck(ipAddress, "sbl.spamhaus.org") ||
                   performDnsBlacklistCheck(ipAddress, "css.spamhaus.org") ||
                   performDnsBlacklistCheck(ipAddress, "pbl.spamhaus.org");
        } catch (Exception e) {
            log.error("Error checking Spamhaus database: {}", e.getMessage());
            return false;
        }
    }

    private boolean checkAbuseIPDatabase(String ipAddress) {
        try {
            // implement AbuseIPDB API check
            // this would make HTTP requests to AbuseIPDB API
            return performHttpThreatCheck(ipAddress, "abuseipdb");
        } catch (Exception e) {
            log.error("Error checking AbuseIP database: {}", e.getMessage());
            return false;
        }
    }

    private boolean checkVirusTotalDatabase(String ipAddress) {
        try {
            // implement VirusTotal API check
            // this would make HTTP requests to VirusTotal API
            return performHttpThreatCheck(ipAddress, "virustotal");
        } catch (Exception e) {
            log.error("Error checking VirusTotal database: {}", e.getMessage());
            return false;
        }
    }

    private boolean checkTorExitNodeList(String ipAddress) {
        try {
            // check against tor project exit node list
            // this would typically download and parse the tor exit list
            Set<String> knownTorExits = getTorExitNodeList();
            return knownTorExits.contains(ipAddress);
        } catch (Exception e) {
            log.error("Error checking tor exit node list: {}", e.getMessage());
            return false;
        }
    }

    private boolean checkProxyVpnDatabase(String ipAddress) {
        try {
            // check against proxy/VPN detection services
            return performHttpThreatCheck(ipAddress, "proxycheck") ||
                   performHttpThreatCheck(ipAddress, "vpnapi");
        } catch (Exception e) {
            log.error("Error checking proxy/VPN database: {}", e.getMessage());
            return false;
        }
    }

    private boolean performDnsBlacklistCheck(String ipAddress, String blacklist) {
        try {
            // reverse IP and query DNS blacklist
            String[] parts = ipAddress.split("\\.");
            String reversedIp = parts[3] + "." + parts[2] + "." + parts[1] + "." + parts[0];
            String query = reversedIp + "." + blacklist;

            InetAddress result = InetAddress.getByName(query);
            return result != null; // if resolves, IP is blacklisted

        } catch (Exception e) {
            // if DNS query fails, IP is not blacklisted
            return false;
        }
    }

    private boolean performHttpThreatCheck(String ipAddress, String service) {
        try {
            // implement HTTP-based threat intelligence checks
            // this would make actual HTTP requests to threat intelligence APIs

            // for now, return false to avoid external dependencies
            // in production, this would integrate with actual APIs
            return false;

        } catch (Exception e) {
            log.error("Error performing HTTP threat check: {}", e.getMessage());
            return false;
        }
    }

    private Set<String> getTorExitNodeList() {
        try {
            String torListKey = "tor_exit_list";
            Set<String> cached = parseStringSet(cacheService.get(torListKey));

            if (cached != null && !cached.isEmpty()) {
                return cached;
            }

            // download and parse tor exit node list
            Set<String> torExits = downloadTorExitList();

            if (!torExits.isEmpty()) {
                cacheService.set(torListKey, serializeStringSet(torExits.toArray(new String[0])));
                cacheService.expire(torListKey, 6, TimeUnit.HOURS);
            }

            return torExits;

        } catch (Exception e) {
            log.error("Error getting tor exit node list: {}", e.getMessage());
            return new HashSet<>();
        }
    }

    private Set<String> downloadTorExitList() {
        try {
            // implement actual download from tor project
            // this would download from https://check.torproject.org/exit-addresses

            // for now, return known tor exit nodes
            return Set.of(
                "***************", "***************", "***************",
                "**************", "*************", "**************"
            );

        } catch (Exception e) {
            log.error("Error downloading tor exit list: {}", e.getMessage());
            return new HashSet<>();
        }
    }

    // security data classes
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionData {
        private String ipAddress;
        private String userAgent;
        private long lastActivity;
        private Long tokenIssuedAt;
        private String deviceFingerprint;
        private long createdAt;
        private Map<String, Object> securityFlags;
        private int activityCount = 0;
        private List<String> ipHistory = new ArrayList<>();

        public void updateSecurityMetrics() {
            this.activityCount++;
            if (!ipHistory.contains(ipAddress)) {
                ipHistory.add(ipAddress);
            }
            if (ipHistory.size() > 10) {
                ipHistory = ipHistory.subList(ipHistory.size() - 10, ipHistory.size());
            }
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginPattern {
        private List<Long> loginTimes = new ArrayList<>();
        private Map<Integer, Integer> hourFrequency = new HashMap<>();
        private Map<Integer, Integer> dayFrequency = new HashMap<>();
        private double averageInterval = 0.0;

        public void addLoginTime(long timestamp) {
            loginTimes.add(timestamp);

            LocalTime time = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()).toLocalTime();

            int hour = time.getHour();
            int dayOfWeek = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()).getDayOfWeek().getValue();

            hourFrequency.put(hour, hourFrequency.getOrDefault(hour, 0) + 1);
            dayFrequency.put(dayOfWeek, dayFrequency.getOrDefault(dayOfWeek, 0) + 1);

            calculateAverageInterval();

            if (loginTimes.size() > 100) {
                loginTimes = loginTimes.subList(loginTimes.size() - 100, loginTimes.size());
            }
        }

        public boolean isAnomalous(long timestamp) {
            if (loginTimes.size() < 5) {
                return false;
            }

            LocalTime time = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()).toLocalTime();

            int hour = time.getHour();
            int dayOfWeek = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()).getDayOfWeek().getValue();

            int hourCount = hourFrequency.getOrDefault(hour, 0);
            double hourPercentage = (double) hourCount / loginTimes.size();

            int dayCount = dayFrequency.getOrDefault(dayOfWeek, 0);
            double dayPercentage = (double) dayCount / loginTimes.size();

            return hourPercentage < 0.05 && dayPercentage < 0.1;
        }

        private void calculateAverageInterval() {
            if (loginTimes.size() < 2) {
                return;
            }

            long totalInterval = 0;
            for (int i = 1; i < loginTimes.size(); i++) {
                totalInterval += loginTimes.get(i) - loginTimes.get(i - 1);
            }

            averageInterval = (double) totalInterval / (loginTimes.size() - 1);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatIntelResult {
        private boolean isThreat;
        private int threatLevel;
        private String threatType;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GeoLocationResult {
        private boolean isSuspicious;
        private int riskScore;
        private String location;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GeoLocationHistory {
        private List<LocationEntry> locations = new ArrayList<>();
        private Set<String> knownLocations = new HashSet<>();

        public void addLocation(String location, long timestamp) {
            locations.add(new LocationEntry(location, timestamp));
            knownLocations.add(location);

            if (locations.size() > 50) {
                locations = locations.subList(locations.size() - 50, locations.size());
            }
        }

        public boolean isLocationSuspicious(String location) {
            if (knownLocations.isEmpty()) {
                return false;
            }
            return !knownLocations.contains(location);
        }

        public int calculateLocationRisk(String location) {
            if (knownLocations.contains(location)) {
                return 0;
            }
            if (knownLocations.size() >= 5) {
                return 40;
            }
            return 20;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class LocationEntry {
            private String location;
            private long timestamp;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BehavioralProfile {
        private Map<String, Double> typingPatterns = new HashMap<>();
        private Map<String, Integer> actionFrequency = new HashMap<>();
        private List<Long> sessionDurations = new ArrayList<>();
        private double averageSessionDuration = 0.0;

        public boolean detectAnomalies(ActivityContext activity) {
            String userAgent = activity.getUserAgent();
            if (typingPatterns.containsKey(userAgent)) {
                return false;
            }
            return false;
        }

        public void updateProfile(ActivityContext activity) {
            String userAgent = activity.getUserAgent();
            actionFrequency.put(userAgent, actionFrequency.getOrDefault(userAgent, 0) + 1);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserRiskProfile {
        private int riskScore = 0;
        private int trustScore = 50;
        private List<String> riskFactors = new ArrayList<>();
        private long lastUpdated = System.currentTimeMillis();
        private int successfulLogins = 0;
        private int failedLogins = 0;
        private int securityIncidents = 0;

        public boolean isHighRisk() {
            return riskScore > 70 || securityIncidents > 3;
        }

        public boolean isTrusted() {
            return trustScore > 80 && successfulLogins > 50 && securityIncidents == 0;
        }

        public void updateRiskScore() {
            int newRiskScore = 0;

            if (failedLogins > successfulLogins * 0.1) {
                newRiskScore += 20;
            }

            if (securityIncidents > 0) {
                newRiskScore += securityIncidents * 15;
            }

            long daysSinceUpdate = (System.currentTimeMillis() - lastUpdated) / (24 * 60 * 60 * 1000);
            if (daysSinceUpdate > 30) {
                newRiskScore = Math.max(0, newRiskScore - 10);
            }

            this.riskScore = Math.min(100, newRiskScore);
            this.lastUpdated = System.currentTimeMillis();
        }
    }

    // helper methods for security data initialization
    private Map<String, Object> initializeSecurityFlags() {
        Map<String, Object> flags = new HashMap<>();
        flags.put("suspicious_activity", false);
        flags.put("new_device", false);
        flags.put("location_change", false);
        flags.put("velocity_check", false);
        return flags;
    }

    private void invalidateSession(String userId) {
        try {
            String sessionKey = "session:" + userId;
            cacheService.delete(sessionKey);
            log.info("Session invalidated for user: {}", maskSensitiveData(userId));
        } catch (Exception e) {
            log.error("Error invalidating session: {}", e.getMessage());
        }
    }

    private boolean validateSessionConsistency(SessionData session, SessionContext context) {
        try {
            // check for drastic IP changes
            if (!session.getIpHistory().contains(context.getIpAddress()) &&
                session.getIpHistory().size() > 0) {

                String lastIp = session.getIpHistory().get(session.getIpHistory().size() - 1);
                if (!isSameNetwork(lastIp, context.getIpAddress())) {
                    return false;
                }
            }

            // check for user agent consistency
            if (!session.getUserAgent().equals(context.getUserAgent())) {
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("Error validating session consistency: {}", e.getMessage());
            return false;
        }
    }

    private boolean detectConcurrentSessions(String userId, SessionData session) {
        try {
            String concurrentKey = "concurrent:" + userId;
            Set<String> activeSessions = parseStringSet(cacheService.get(concurrentKey));

            if (activeSessions != null && activeSessions.size() > 3) {
                return true; // too many concurrent sessions
            }

            return false;

        } catch (Exception e) {
            log.error("Error detecting concurrent sessions: {}", e.getMessage());
            return false;
        }
    }

    private boolean isSameNetwork(String ip1, String ip2) {
        try {
            String[] parts1 = ip1.split("\\.");
            String[] parts2 = ip2.split("\\.");

            // check if same /24 network
            return parts1[0].equals(parts2[0]) &&
                   parts1[1].equals(parts2[1]) &&
                   parts1[2].equals(parts2[2]);

        } catch (Exception e) {
            return false;
        }
    }

    private void logSecurityEvent(String userId, ActivityContext activity, int riskScore,
                                 List<String> reasons, String action) {
        try {
            Map<String, Object> event = new HashMap<>();
            event.put("userId", maskSensitiveData(userId));
            event.put("ipAddress", maskSensitiveData(activity.getIpAddress()));
            event.put("userAgent", activity.getUserAgent().substring(0, Math.min(50, activity.getUserAgent().length())));
            event.put("riskScore", riskScore);
            event.put("reasons", reasons);
            event.put("action", action);
            event.put("timestamp", System.currentTimeMillis());

            log.warn("Security event: {}", objectMapper.writeValueAsString(event));

        } catch (Exception e) {
            log.error("Error logging security event: {}", e.getMessage());
        }
    }
    
    // result classes
    public static class BruteForceResult {
        private boolean allowed;
        private Long lockoutTime;
        private String reason;
        
        public static BruteForceResultBuilder builder() {
            return new BruteForceResultBuilder();
        }
        
        public static class BruteForceResultBuilder {
            private boolean allowed;
            private Long lockoutTime;
            private String reason;
            
            public BruteForceResultBuilder allowed(boolean allowed) {
                this.allowed = allowed;
                return this;
            }
            
            public BruteForceResultBuilder lockoutTime(Long lockoutTime) {
                this.lockoutTime = lockoutTime;
                return this;
            }
            
            public BruteForceResultBuilder reason(String reason) {
                this.reason = reason;
                return this;
            }
            
            public BruteForceResult build() {
                BruteForceResult result = new BruteForceResult();
                result.allowed = this.allowed;
                result.lockoutTime = this.lockoutTime;
                result.reason = this.reason;
                return result;
            }
        }
        
        // getters
        public boolean isAllowed() { return allowed; }
        public Long getLockoutTime() { return lockoutTime; }
        public String getReason() { return reason; }
    }
    
    public static class RateLimitResult {
        private boolean allowed;
        private int remaining;
        private Long resetTime;
        private String reason;
        
        public static RateLimitResultBuilder builder() {
            return new RateLimitResultBuilder();
        }
        
        public static class RateLimitResultBuilder {
            private boolean allowed;
            private int remaining;
            private Long resetTime;
            private String reason;
            
            public RateLimitResultBuilder allowed(boolean allowed) {
                this.allowed = allowed;
                return this;
            }
            
            public RateLimitResultBuilder remaining(int remaining) {
                this.remaining = remaining;
                return this;
            }
            
            public RateLimitResultBuilder resetTime(Long resetTime) {
                this.resetTime = resetTime;
                return this;
            }
            
            public RateLimitResultBuilder reason(String reason) {
                this.reason = reason;
                return this;
            }
            
            public RateLimitResult build() {
                RateLimitResult result = new RateLimitResult();
                result.allowed = this.allowed;
                result.remaining = this.remaining;
                result.resetTime = this.resetTime;
                result.reason = this.reason;
                return result;
            }
        }
        
        // getters
        public boolean isAllowed() { return allowed; }
        public int getRemaining() { return remaining; }
        public Long getResetTime() { return resetTime; }
        public String getReason() { return reason; }
    }
    
    public static class SuspiciousActivityResult {
        private boolean isSuspicious;
        private int riskScore;
        private List<String> reasons;
        private String action;
        
        public static SuspiciousActivityResultBuilder builder() {
            return new SuspiciousActivityResultBuilder();
        }
        
        public static class SuspiciousActivityResultBuilder {
            private boolean isSuspicious;
            private int riskScore;
            private List<String> reasons;
            private String action;
            
            public SuspiciousActivityResultBuilder isSuspicious(boolean isSuspicious) {
                this.isSuspicious = isSuspicious;
                return this;
            }
            
            public SuspiciousActivityResultBuilder riskScore(int riskScore) {
                this.riskScore = riskScore;
                return this;
            }
            
            public SuspiciousActivityResultBuilder reasons(List<String> reasons) {
                this.reasons = reasons;
                return this;
            }
            
            public SuspiciousActivityResultBuilder action(String action) {
                this.action = action;
                return this;
            }
            
            public SuspiciousActivityResult build() {
                SuspiciousActivityResult result = new SuspiciousActivityResult();
                result.isSuspicious = this.isSuspicious;
                result.riskScore = this.riskScore;
                result.reasons = this.reasons;
                result.action = this.action;
                return result;
            }
        }
        
        // getters
        public boolean isSuspicious() { return isSuspicious; }
        public int getRiskScore() { return riskScore; }
        public List<String> getReasons() { return reasons; }
        public String getAction() { return action; }
    }
    
    public static class SessionValidationResult {
        private boolean isValid;
        private String action;
        
        public static SessionValidationResultBuilder builder() {
            return new SessionValidationResultBuilder();
        }
        
        public static class SessionValidationResultBuilder {
            private boolean isValid;
            private String action;
            
            public SessionValidationResultBuilder isValid(boolean isValid) {
                this.isValid = isValid;
                return this;
            }
            
            public SessionValidationResultBuilder action(String action) {
                this.action = action;
                return this;
            }
            
            public SessionValidationResult build() {
                SessionValidationResult result = new SessionValidationResult();
                result.isValid = this.isValid;
                result.action = this.action;
                return result;
            }
        }
        
        // getters
        public boolean isValid() { return isValid; }
        public String getAction() { return action; }
    }
    
    // context 
    public static class ActivityContext {
        private String ipAddress;
        private String userAgent;
        private long timestamp;
        
        public ActivityContext(String ipAddress, String userAgent, long timestamp) {
            this.ipAddress = ipAddress;
            this.userAgent = userAgent;
            this.timestamp = timestamp;
        }
        
        public String getIpAddress() { return ipAddress; }
        public String getUserAgent() { return userAgent; }
        public long getTimestamp() { return timestamp; }
    }
    
    public static class SessionContext {
        private String ipAddress;
        private String userAgent;
        private Long tokenIssuedAt;
        
        public SessionContext(String ipAddress, String userAgent, Long tokenIssuedAt) {
            this.ipAddress = ipAddress;
            this.userAgent = userAgent;
            this.tokenIssuedAt = tokenIssuedAt;
        }
        
        public String getIpAddress() { return ipAddress; }
        public String getUserAgent() { return userAgent; }
        public Long getTokenIssuedAt() { return tokenIssuedAt; }
    }

    
    // Helper methods for Set operations with cache service
    private Set<String> parseStringSet(String data) {
        if (data == null || data.trim().isEmpty()) {
            return new HashSet<>();
        }
        try {
            return new HashSet<>(Arrays.asList(data.split(",")));
        } catch (Exception e) {
            return new HashSet<>();
        }
    }
    
    private String serializeStringSet(Set<String> set) {
        if (set == null || set.isEmpty()) {
            return "";
        }
        return String.join(",", set);
    }
    
    private String serializeStringSet(String... items) {
        if (items == null || items.length == 0) {
            return "";
        }
        return String.join(",", items);
    }

}
