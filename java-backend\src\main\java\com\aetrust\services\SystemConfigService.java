package com.aetrust.services;

import com.aetrust.models.SystemConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.aetrust.repositories.SystemConfigRepository;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SystemConfigService {
    
    @Autowired
    private SystemConfigRepository configRepository;
    

    @Autowired
    private CacheService cacheService;
    
    private static final String CONFIG_CACHE_PREFIX = "config:";
    private static final long CACHE_TTL = 300; // 5 minutes
    
  
    public String getConfig(String key, String defaultValue) {
        try {
            String cacheKey = CONFIG_CACHE_PREFIX + key;
            String cachedValue = cacheService.get(cacheKey);

            if (cachedValue != null) {
                return cachedValue;
            }

            SystemConfig config = configRepository.findByConfigKeyAndIsActive(key, true).orElse(null);

            String value = config != null ? config.getStringValue() : defaultValue;

            cacheService.set(cacheKey, value, CACHE_TTL, TimeUnit.SECONDS);
            
            return value;
            
        } catch (Exception error) {
            log.error("Error getting config {}: {}", key, error.getMessage());
            return defaultValue;
        }
    }
    
  
    public int getIntConfig(String key, int defaultValue) {
        try {
            String value = getConfig(key, String.valueOf(defaultValue));
            return Integer.parseInt(value);
        } catch (NumberFormatException error) {
            log.warn("Invalid integer config value for {}: {}", key, error.getMessage());
            return defaultValue;
        }
    }
    
 
    public long getLongConfig(String key, long defaultValue) {
        try {
            String value = getConfig(key, String.valueOf(defaultValue));
            return Long.parseLong(value);
        } catch (NumberFormatException error) {
            log.warn("Invalid long config value for {}: {}", key, error.getMessage());
            return defaultValue;
        }
    }
    
   
    public double getDoubleConfig(String key, double defaultValue) {
        try {
            String value = getConfig(key, String.valueOf(defaultValue));
            return Double.parseDouble(value);
        } catch (NumberFormatException error) {
            log.warn("Invalid double config value for {}: {}", key, error.getMessage());
            return defaultValue;
        }
    }
    
 
    public boolean getBooleanConfig(String key, boolean defaultValue) {
        try {
            String value = getConfig(key, String.valueOf(defaultValue));
            return Boolean.parseBoolean(value);
        } catch (Exception error) {
            log.warn("Invalid boolean config value for {}: {}", key, error.getMessage());
            return defaultValue;
        }
    }
    

    public SystemConfig setConfig(String key, String value, String updatedBy) {
        try {
            SystemConfig existingConfig = configRepository.findByConfigKey(key).orElse(null);

            if (existingConfig != null) {
                existingConfig.setConfigValue(value);
                existingConfig.setUpdatedBy(updatedBy);
                existingConfig.setUpdatedAt(LocalDateTime.now());

                SystemConfig savedConfig = configRepository.save(existingConfig);

                String cacheKey = CONFIG_CACHE_PREFIX + key;
                cacheService.delete(cacheKey);

                return savedConfig;
            } else {
                SystemConfig newConfig = new SystemConfig();
                newConfig.setConfigKey(key);
                newConfig.setConfigValue(value);
                newConfig.setConfigType(determineConfigType(value));
                newConfig.setCreatedBy(updatedBy);
                newConfig.setUpdatedBy(updatedBy);
                newConfig.setCreatedAt(LocalDateTime.now());
                newConfig.setUpdatedAt(LocalDateTime.now());
                
                return configRepository.save(newConfig);
            }
            
        } catch (Exception error) {
            log.error("Error setting config {}: {}", key, error.getMessage());
            return null;
        }
    }
    
  
    public boolean deleteConfig(String key) {
        try {
            SystemConfig config = configRepository.findByConfigKey(key).orElse(null);
            if (config != null) {
                configRepository.delete(config);
            }

            String cacheKey = CONFIG_CACHE_PREFIX + key;
            cacheService.delete(cacheKey);

            return true;
            
        } catch (Exception error) {
            log.error("Error deleting config {}: {}", key, error.getMessage());
            return false;
        }
    }
    

    public void invalidateCache(String key) {
        try {
            String cacheKey = CONFIG_CACHE_PREFIX + key;
            cacheService.delete(cacheKey);
        } catch (Exception error) {
            log.error("Error invalidating cache for {}: {}", key, error.getMessage());
        }
    }
    
  
    public void invalidateAllCache() {
        try {
            String pattern = CONFIG_CACHE_PREFIX + "*";
            // Clear all config cache keys from cache storage
            for (String key : cacheService.keys(pattern)) {
                if (key.startsWith(CONFIG_CACHE_PREFIX)) {
                    cacheService.delete(key);
                }
            }
        } catch (Exception error) {
            log.error("Error invalidating all config cache: {}", error.getMessage());
        }
    }
    
   
    public boolean configExists(String key) {
        try {
            return configRepository.findByConfigKeyAndIsActive(key, true).isPresent();
        } catch (Exception error) {
            log.error("Error checking config existence {}: {}", key, error.getMessage());
            return false;
        }
    }
    
   
    public java.util.List<SystemConfig> getConfigsByCategory(String category) {
        try {
            return configRepository.findByCategoryAndIsActive(category, true);
        } catch (Exception error) {
            log.error("Error getting configs by category {}: {}", category, error.getMessage());
            return new java.util.ArrayList<>();
        }
    }
    
 
    private String determineConfigType(String value) {
        if (value == null) return "string";

        // Try to determine type from string value
        try {
            Integer.parseInt(value);
            return "integer";
        } catch (NumberFormatException e1) {
            try {
                Double.parseDouble(value);
                return "double";
            } catch (NumberFormatException e2) {
                if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
                    return "boolean";
                }
                return "string";
            }
        }
    }
    
  
    public void initializeDefaultConfigs() {
        try {
            setDefaultConfig("securityMaxLoginAttempts", 5, "system");
            setDefaultConfig("securityLockoutDuration", 900000L, "system");
            setDefaultConfig("securityIpRateLimit", 100, "system");
            setDefaultConfig("securityUserRateLimit", 50, "system");
            setDefaultConfig("securityRateLimitWindow", 3600000L, "system");
            setDefaultConfig("securityBlockThreshold", 70, "system");
            setDefaultConfig("securityChallengeThreshold", 40, "system");
            
            setDefaultConfig("fraudVelocityThreshold", 5, "system");
            setDefaultConfig("fraudAmountThreshold", 10000.0, "system");
            setDefaultConfig("fraudLocationCheckEnabled", true, "system");
            
            log.info("Default system configurations initialized");
            
        } catch (Exception error) {
            log.error("Error initializing default configs: {}", error.getMessage());
        }
    }
    
    private void setDefaultConfig(String key, Object value, String createdBy) {
        if (!configExists(key)) {
            setConfig(key, value != null ? value.toString() : "", createdBy);
        }
    }
}
