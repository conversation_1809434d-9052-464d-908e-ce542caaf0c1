package com.aetrust.services;

import com.aetrust.utils.CryptoUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserCacheService {
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${aetrust.cache.user.ttl-minutes:30}")
    private int userCacheTtlMinutes;
    
    @Value("${aetrust.cache.user.max-size:1000}")
    private int maxCacheSize;
    
    @Value("${aetrust.cache.user.cleanup-interval-minutes:15}")
    private int cleanupIntervalMinutes;
    
    // In-memory cache with expiration tracking
    private final ConcurrentHashMap<String, CacheEntry> userCache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    
    /**
     * Cache entry with expiration tracking
     */
    private static class CacheEntry {
        private final Map<String, Object> data;
        private final LocalDateTime expiresAt;
        private final LocalDateTime createdAt;
        private volatile LocalDateTime lastAccessedAt;
        
        public CacheEntry(Map<String, Object> data, int ttlMinutes) {
            this.data = data;
            this.createdAt = LocalDateTime.now();
            this.expiresAt = this.createdAt.plusMinutes(ttlMinutes);
            this.lastAccessedAt = this.createdAt;
        }
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiresAt);
        }
        
        public Map<String, Object> getData() {
            this.lastAccessedAt = LocalDateTime.now();
            return data;
        }
        
        public LocalDateTime getLastAccessedAt() {
            return lastAccessedAt;
        }
        
        public LocalDateTime getCreatedAt() {
            return createdAt;
        }
    }
    
    /**
     * Initialize the cache service and start cleanup scheduler
     */
    public void init() {
        // Schedule periodic cleanup of expired entries
        cleanupExecutor.scheduleAtFixedRate(
            this::cleanupExpiredEntries, 
            cleanupIntervalMinutes, 
            cleanupIntervalMinutes, 
            TimeUnit.MINUTES
        );
        
        log.info("UserCacheService initialized with TTL: {} minutes, Max size: {}, Cleanup interval: {} minutes",
            userCacheTtlMinutes, maxCacheSize, cleanupIntervalMinutes);
    }
    
    /**
     * Cache user data with automatic expiration
     */
    public void cacheUserData(String userId, Map<String, Object> userData) {
        try {
            if (userId == null || userData == null) {
                log.warn("Cannot cache null user data for userId: {}", userId);
                return;
            }
            
            // Check cache size limit
            if (userCache.size() >= maxCacheSize) {
                evictLeastRecentlyUsed();
            }
            
            // Create secure cache key
            String cacheKey = createCacheKey(userId);
            
            // Create sanitized copy of user data (remove sensitive information)
            Map<String, Object> sanitizedData = sanitizeUserData(userData);
            
            // Create cache entry
            CacheEntry entry = new CacheEntry(sanitizedData, userCacheTtlMinutes);
            userCache.put(cacheKey, entry);
            
            log.debug("Cached user data for userId: {} (cache size: {})", 
                cryptoUtils.maskSensitiveData(userId, 3), userCache.size());
                
        } catch (Exception e) {
            log.error("Error caching user data for userId: {}: {}", userId, e.getMessage());
        }
    }
    
    /**
     * Retrieve cached user data
     */
    public Map<String, Object> getUserData(String userId) {
        try {
            if (userId == null) {
                return null;
            }
            
            String cacheKey = createCacheKey(userId);
            CacheEntry entry = userCache.get(cacheKey);
            
            if (entry == null) {
                log.debug("Cache miss for userId: {}", cryptoUtils.maskSensitiveData(userId, 3));
                return null;
            }
            
            if (entry.isExpired()) {
                userCache.remove(cacheKey);
                log.debug("Cache entry expired for userId: {}", cryptoUtils.maskSensitiveData(userId, 3));
                return null;
            }
            
            log.debug("Cache hit for userId: {}", cryptoUtils.maskSensitiveData(userId, 3));
            return entry.getData();
            
        } catch (Exception e) {
            log.error("Error retrieving cached user data for userId: {}: {}", userId, e.getMessage());
            return null;
        }
    }
    
    /**
     * Invalidate cached user data
     */
    public void invalidateUserData(String userId) {
        try {
            if (userId == null) {
                return;
            }
            
            String cacheKey = createCacheKey(userId);
            CacheEntry removed = userCache.remove(cacheKey);
            
            if (removed != null) {
                log.debug("Invalidated cache for userId: {}", cryptoUtils.maskSensitiveData(userId, 3));
            }
            
        } catch (Exception e) {
            log.error("Error invalidating cache for userId: {}: {}", userId, e.getMessage());
        }
    }
    
    /**
     * Clear all cached data
     */
    public void clearAll() {
        int size = userCache.size();
        userCache.clear();
        log.info("Cleared all cached user data ({} entries)", size);
    }
    
    /**
     * Get cache statistics
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalEntries", userCache.size());
        stats.put("maxSize", maxCacheSize);
        stats.put("ttlMinutes", userCacheTtlMinutes);
        stats.put("cleanupIntervalMinutes", cleanupIntervalMinutes);
        
        // Calculate expired entries
        long expiredCount = userCache.values().stream()
            .mapToLong(entry -> entry.isExpired() ? 1 : 0)
            .sum();
        stats.put("expiredEntries", expiredCount);
        
        return stats;
    }
    
    /**
     * Create secure cache key for user ID
     */
    private String createCacheKey(String userId) {
        // Use hash to prevent direct user ID exposure in cache keys
        return "user:" + cryptoUtils.generateSecureToken(8) + ":" + userId.hashCode();
    }
    
    /**
     * Sanitize user data to remove sensitive information before caching
     */
    private Map<String, Object> sanitizeUserData(Map<String, Object> userData) {
        try {
            // Create deep copy to avoid modifying original data
            String jsonString = objectMapper.writeValueAsString(userData);
            @SuppressWarnings("unchecked")
            Map<String, Object> sanitized = objectMapper.readValue(jsonString, Map.class);
            
            // Remove sensitive fields
            removeSensitiveFields(sanitized);
            
            return sanitized;
            
        } catch (JsonProcessingException e) {
            log.error("Error sanitizing user data: {}", e.getMessage());
            return new HashMap<>();
        }
    }
    
    /**
     * Remove sensitive fields from user data
     */
    @SuppressWarnings("unchecked")
    private void removeSensitiveFields(Map<String, Object> data) {
        // Remove sensitive fields at root level
        data.remove("password");
        data.remove("passwordHash");
        data.remove("salt");
        data.remove("privateKey");
        data.remove("secretKey");
        
        // Remove sensitive fields from nested objects
        if (data.get("security") instanceof Map) {
            Map<String, Object> security = (Map<String, Object>) data.get("security");
            security.remove("password");
            security.remove("passwordHash");
            security.remove("salt");
            security.remove("secretAnswer");
            security.remove("backupCodes");
        }
        
        if (data.get("identity") instanceof Map) {
            Map<String, Object> identity = (Map<String, Object>) data.get("identity");
            identity.remove("documentNumber");
            identity.remove("ssn");
            identity.remove("taxId");
        }
        
        // Add cache metadata
        data.put("cached", true);
        data.put("cachedAt", LocalDateTime.now());
    }
    
    /**
     * Clean up expired cache entries
     */
    private void cleanupExpiredEntries() {
        try {
            int initialSize = userCache.size();
            
            userCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
            
            int removedCount = initialSize - userCache.size();
            if (removedCount > 0) {
                log.debug("Cleaned up {} expired cache entries (remaining: {})", 
                    removedCount, userCache.size());
            }
            
        } catch (Exception e) {
            log.error("Error during cache cleanup: {}", e.getMessage());
        }
    }
    
    /**
     * Evict least recently used entries when cache is full
     */
    private void evictLeastRecentlyUsed() {
        try {
            // Find the least recently used entry
            String lruKey = userCache.entrySet().stream()
                .min((e1, e2) -> e1.getValue().getLastAccessedAt()
                    .compareTo(e2.getValue().getLastAccessedAt()))
                .map(Map.Entry::getKey)
                .orElse(null);
            
            if (lruKey != null) {
                userCache.remove(lruKey);
                log.debug("Evicted LRU cache entry (cache size: {})", userCache.size());
            }
            
        } catch (Exception e) {
            log.error("Error during LRU eviction: {}", e.getMessage());
        }
    }
    
    /**
     * Shutdown the cache service
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        clearAll();
        log.info("UserCacheService shutdown completed");
    }
}
