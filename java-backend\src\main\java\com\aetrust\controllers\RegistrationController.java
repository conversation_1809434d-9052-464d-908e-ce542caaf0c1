package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.RegistrationService;
import com.aetrust.services.SecurityService;
import com.aetrust.services.ValidationService;
import com.aetrust.utils.ControllerUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/auth/registration")
@Validated
public class RegistrationController {
    
    @Autowired
    private RegistrationService registrationService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private ValidationService validationService;

    @Autowired
    private ControllerUtils controllerUtils;
    
    @PostMapping("/initiate")
    public ResponseEntity<ApiResponse<Map<String, Object>>> initiateRegistration(
            @Valid @RequestBody RegistrationInitRequest request,
            HttpServletRequest httpRequest) {

        try {
            if (controllerUtils.isEmptyRequest(request)) {
                return controllerUtils.handleEmptyRequest("registration initiation");
            }

            if (!validationService.isValidEmail(request.getEmail()) ||
                !validationService.isValidPhone(request.getPhone())) {
                log.warn("Invalid input in registration initiation from IP: {}", controllerUtils.getClientIp(httpRequest));
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Invalid input", "INVALID_INPUT"));
            }


            controllerUtils.setRequestMetadata(request, httpRequest);

            String ipAddress = request.getIpAddress();
            String userAgent = request.getUserAgent();

            log.info("Registration initiated - phone: {}, email: {}, IP: {}, UserAgent: {}",
                validationService.sanitizeInput(request.getPhone().substring(0, Math.min(request.getPhone().length(), 6)) + "***"),
                validationService.sanitizeInput(request.getEmail().replaceAll("(.{2}).*(@.*)", "$1***$2")),
                ipAddress,
                validationService.sanitizeInput(userAgent != null ? userAgent.substring(0, Math.min(userAgent.length(), 50)) : "unknown"));

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/register/initiate");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            SecurityService.ActivityContext activity = new SecurityService.ActivityContext(
                ipAddress, userAgent, System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity("anonymous", activity);
            
            if (suspiciousResult.getRiskScore() > 50) {
                return ResponseEntity.status(403).body(
                    ApiResponse.error("registration blocked due to suspicious activity", "SUSPICIOUS_ACTIVITY"));
            }
            
            RegistrationService.RegistrationResult result = registrationService.initiateRegistration(request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Registration failed", "REGISTRATION_FAILED"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationId", result.getRegistrationId());
            responseData.put("nextStep", result.getNextStep());
            responseData.put("phoneVerificationRequired", true);
            responseData.put("expiresIn", 900); 

            String responseMessage = result.getMessage();

            return ResponseEntity.ok(
                ApiResponse.success(responseMessage, responseData));
                
        } catch (Exception error) {
            log.error("Error initiating registration: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("registration initiation failed", "INTERNAL_ERROR"));
        }
    }
    
@PostMapping("/verify-phone")
public ResponseEntity<ApiResponse<Map<String, Object>>> verifyPhone(
        @RequestBody(required = false) String rawBody,
        HttpServletRequest httpRequest) {

    try {

         // Check if request body is null or empty
        if (rawBody == null || rawBody.trim().isEmpty()) {
            log.warn("Empty request body from IP: {}", controllerUtils.getClientIp(httpRequest));
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Request body is required", "MISSING_REQUEST_BODY"));
        }

        if (controllerUtils.isEmptyBody(rawBody)) {
            return controllerUtils.handleEmptyRequest("phone verification");
        }

        ObjectMapper objectMapper = new ObjectMapper();
        PhoneVerificationRequest request;

        try {
            request = objectMapper.readValue(rawBody, PhoneVerificationRequest.class);
        } catch (JsonProcessingException e) {
            return controllerUtils.handleJsonError(controllerUtils.getClientIp(httpRequest));
        }

        if (controllerUtils.isEmptyRequest(request)) {
            return controllerUtils.handleEmptyRequest("phone verification");
        }

        if (request == null ||
            request.getPhone() == null || request.getPhone().trim().isEmpty() ||
            request.getCode() == null || request.getCode().trim().isEmpty()) {
            log.warn("Missing required fields from IP: {}", controllerUtils.getClientIp(httpRequest));
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Phone and code are required", "MISSING_REQUIRED_FIELDS"));
        }

        if (!validationService.isValidPhone(request.getPhone()) ||
            !validationService.isValidOtpCode(request.getCode())) {
            log.warn("Invalid phone verification input from IP: {}", controllerUtils.getClientIp(httpRequest));
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Invalid input", "INVALID_INPUT"));
        }
        
        String ipAddress = controllerUtils.getClientIp(httpRequest);
        
        SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
            request.getPhone(), "user", "/auth/register/verify-phone");
        
        if (!rateLimit.isAllowed()) {
            return ResponseEntity.status(429).body(
                ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
        }
        
        RegistrationService.VerificationResult result = registrationService.verifyPhone(request);

        log.info("Phone verification result - Success: {}, Message: {}, ErrorCode: {}",
            result.isSuccess(), result.getMessage(), result.getErrorCode());

        if (!result.isSuccess()) {
            log.warn("Phone verification failed for {}: {}", request.getPhone(), result.getMessage());
            return ResponseEntity.badRequest().body(
                ApiResponse.error("Verification failed", "VERIFICATION_FAILED"));
        }
        
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("phoneVerified", true);
        responseData.put("nextStep", result.getNextStep());
        responseData.put("registrationId", result.getRegistrationId());
        
        if (result.isCompleted()) {
            responseData.put("accessToken", result.getAccessToken());
            responseData.put("refreshToken", result.getRefreshToken());
            responseData.put("userProfile", result.getUserProfile());
        }
        
        return ResponseEntity.ok(
            ApiResponse.success("phone verification successful", responseData));
            
    } catch (Exception error) {
        log.error("Error verifying phone: {}", error.getMessage());
        return ResponseEntity.status(500).body(
            ApiResponse.error("phone verification failed", "INTERNAL_ERROR"));
    }
}
    
    @PostMapping("/verify-email")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyEmail(
            @Valid @RequestBody EmailVerificationRequest request,
            HttpServletRequest httpRequest) {

        try {

            if (!validationService.isValidEmail(request.getEmail()) ||
                !validationService.isValidOtpCode(request.getCode())) {
                log.warn("Invalid email verification input from IP: {}", controllerUtils.getClientIp(httpRequest));
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Invalid input", "INVALID_INPUT"));
            }
            String ipAddress = controllerUtils.getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getEmail(), "user", "/auth/register/verify-email");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            RegistrationService.VerificationResult result = registrationService.verifyEmail(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("emailVerified", true);
            responseData.put("nextStep", result.getNextStep());
            responseData.put("registrationId", result.getRegistrationId());
            
            return ResponseEntity.ok(
                ApiResponse.success("email verification successful", responseData));
                
        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("email verification failed", "INTERNAL_ERROR"));
        }
    }



    @PostMapping("/id-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitIdVerification(
            @Valid @RequestBody IdVerificationRequest request,
            HttpServletRequest httpRequest) {

        try {
            if (controllerUtils.isEmptyRequest(request)) {
                return controllerUtils.handleEmptyRequest("ID verification");
            }

            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/register/id-verification");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.IdVerificationResult result = registrationService.submitIdVerification(request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("idVerificationSubmitted", true);
            responseData.put("userId", result.getUserId());
            responseData.put("verificationStatus", "pending");
            responseData.put("nextStep", result.getNextStep());

            return ResponseEntity.ok(
                ApiResponse.success("ID verification submitted successfully", responseData));

        } catch (Exception error) {
            log.error("Error submitting ID verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("ID verification failed", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/set-pin")
    public ResponseEntity<ApiResponse<Map<String, Object>>> setTransactionPin(
            @Valid @RequestBody SetTransactionPinRequest request,
            HttpServletRequest httpRequest) {

        try {
            if (controllerUtils.isEmptyRequest(request)) {
                return controllerUtils.handleEmptyRequest("transaction PIN setup");
            }

            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/register/set-pin");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.PinSetupResult result = registrationService.setTransactionPin(request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationCompleted", true);
            responseData.put("userId", result.getUserId());
            responseData.put("accessToken", result.getAccessToken());
            responseData.put("refreshToken", result.getRefreshToken());
            responseData.put("userProfile", result.getUserProfile());

            log.info("Registration completed successfully for user: {}", result.getUserId());

            return ResponseEntity.ok(
                ApiResponse.success("Registration completed successfully", responseData));

        } catch (Exception error) {
            log.error("Error setting transaction PIN: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("PIN setup failed", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/business-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitBusinessVerification(
            @Valid @RequestBody BusinessVerificationRequest request,
            HttpServletRequest httpRequest) {

        try {
            if (controllerUtils.isEmptyRequest(request)) {
                return controllerUtils.handleEmptyRequest("business verification");
            }

            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getPhone(), "user", "/auth/register/business-verification");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.BusinessVerificationResult result = registrationService.submitBusinessVerification(request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("businessVerificationSubmitted", true);
            responseData.put("userId", result.getUserId());
            responseData.put("verificationStatus", "pending");
            responseData.put("nextStep", result.getNextStep());
            responseData.put("estimatedProcessingTime", "3-5 business days");

            return ResponseEntity.ok(
                ApiResponse.success("business verification submitted successfully", responseData));

        } catch (Exception error) {
            log.error("Error submitting business verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("business verification failed", "INTERNAL_ERROR"));
        }
    }


    @PostMapping("/send-email-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> sendEmailVerification(
            @Valid @RequestBody SendEmailVerificationRequest request,
            HttpServletRequest httpRequest) {

        try {
            if (controllerUtils.isEmptyRequest(request)) {
                return controllerUtils.handleEmptyRequest("send email verification");
            }

            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getEmail(), "user", "/auth/register/send-email-verification");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.VerificationResult result = registrationService.sendEmailVerificationCode(request.getEmail());

            if (!result.isSuccess()) {
                log.warn("Email verification failed for {}: {}",
                    validationService.sanitizeInput(request.getEmail()), result.getMessage());
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Verification failed", "VERIFICATION_FAILED"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("emailSent", true);
            responseData.put("nextStep", result.getNextStep());

            if (result.getVerificationCode() != null) {
                responseData.put("note", "Email service unavailable - code sent via alternative method");
                log.debug("Email service unavailable, verification code generated");
            }

            return ResponseEntity.ok(
                ApiResponse.success(result.getMessage(), responseData));

        } catch (Exception error) {
            log.error("Error sending email verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to send email verification", "INTERNAL_ERROR"));
        }
    }





    @PostMapping("/resend-verification")
    public ResponseEntity<ApiResponse<Map<String, Object>>> resendVerification(
            @Valid @RequestBody ResendVerificationRequest request,
            HttpServletRequest httpRequest) {

        try {
            if (controllerUtils.isEmptyRequest(request)) {
                return controllerUtils.handleEmptyRequest("resend verification");
            }

            if (!request.isValid()) {
                String errorMsg = "email".equals(request.getVerificationType()) ?
                    "email is required for email verification" :
                    "phone is required for SMS verification";
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(errorMsg, "INVALID_INPUT"));
            }

            String ipAddress = controllerUtils.getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");

            request.setIpAddress(ipAddress);
            request.setUserAgent(userAgent);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getIdentifier(), "user", "/auth/register/resend-verification");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many resend attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.ResendResult result = registrationService.resendVerificationCode(request);

            if (!result.isSuccess()) {
                log.warn("Verification resend failed for {}: {}",
                    validationService.sanitizeInput(request.getIdentifier()), result.getMessage());
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Operation failed", "OPERATION_FAILED"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", true);
            responseData.put("sent", true);
            responseData.put("type", result.getCodeType());
            responseData.put("expires_in", 300);
            responseData.put("retry_after", 60);

            if (result.getVerificationCode() != null) {
                responseData.put("service_status", "unavailable");
                responseData.put("note", "Code sent via alternative method");
                log.debug("Verification service unavailable, code generated");
            } else {
                responseData.put("service_status", "sent");
            }

            return ResponseEntity.ok(
                ApiResponse.success(result.getMessage(), responseData));

        } catch (Exception error) {
            log.error("Error resending verification: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to resend verification", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/validation-requirements")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getValidationRequirements() {
        Map<String, Object> requirements = new HashMap<>();

        Map<String, Object> step1 = new HashMap<>();
        step1.put("required_fields", Arrays.asList("email", "phone", "firstName", "lastName", "dateOfBirth", "password", "platform"));
        step1.put("phone_format", "International format starting with + (e.g., +250788123456)");
        step1.put("email_format", "Valid email address");
        step1.put("password_requirements", "At least 8 characters with uppercase, lowercase, number and special character");
        step1.put("platform_values", Arrays.asList("web", "app", "mobile_web"));
        requirements.put("step1_initiate", step1);

        Map<String, Object> step2 = new HashMap<>();
        step2.put("required_fields", Arrays.asList("userId", "idType", "idNumber", "idPhotoBase64", "platform"));
        step2.put("id_types", Arrays.asList("national_id", "passport", "drivers_license"));
        step2.put("id_photo_format", "Base64 encoded image string");
        requirements.put("step2_id_verification", step2);

        Map<String, Object> step3 = new HashMap<>();
        step3.put("required_fields", Arrays.asList("userId", "pin", "confirmPin", "platform"));
        step3.put("pin_format", "4-digit numeric PIN");
        requirements.put("step3_set_pin", step3);

        return ResponseEntity.ok(
            ApiResponse.success("registration validation requirements", requirements));
    }


    @PostMapping("/check-status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkRegistrationStatus(
            @Valid @RequestBody CheckRegistrationStatusRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/register/check-status");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many requests", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.StatusResult result = registrationService.getRegistrationStatus(request.getIdentifier());

            if (!result.isFound()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("no registration found", "REGISTRATION_NOT_FOUND"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationFound", true);
            responseData.put("currentStep", result.getCurrentStep());
            responseData.put("phoneVerified", result.isPhoneVerified());
            responseData.put("emailVerified", result.isEmailVerified());
            responseData.put("completed", result.isCompleted());
            responseData.put("canContinue", !result.isExpired() && !result.isCompleted());
            responseData.put("nextAction", getNextAction(result));

            if (result.isExpired()) {
                responseData.put("message", "registration expired, please start over");
                responseData.put("requiresRestart", true);
            } else if (result.isCompleted()) {
                responseData.put("message", "registration already completed");
                responseData.put("requiresLogin", true);
            } else {
                responseData.put("message", "registration in progress");
                responseData.put("requiresLogin", false);
            }

            return ResponseEntity.ok(
                ApiResponse.success("registration status retrieved", responseData));

        } catch (Exception error) {
            log.error("Error checking registration status: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to check registration status", "INTERNAL_ERROR"));
        }
    }

    @PostMapping("/continue")
    public ResponseEntity<ApiResponse<Map<String, Object>>> continueRegistration(
            @Valid @RequestBody ContinueRegistrationRequest request,
            HttpServletRequest httpRequest) {

        try {
            String ipAddress = controllerUtils.getClientIp(httpRequest);

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getIdentifier(), "user", "/auth/register/continue");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many attempts", "RATE_LIMIT_EXCEEDED"));
            }

            RegistrationService.StatusResult status = registrationService.getRegistrationStatus(request.getIdentifier());

            if (!status.isFound()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("no registration found", "REGISTRATION_NOT_FOUND"));
            }

            if (status.isExpired()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("registration expired", "REGISTRATION_EXPIRED"));
            }

            if (status.isCompleted()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("registration already completed", "ALREADY_COMPLETED"));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationId", status.getRegistrationId());
            responseData.put("currentStep", status.getCurrentStep());
            responseData.put("nextStep", getNextStep(status.getCurrentStep()));
            responseData.put("phoneVerified", status.isPhoneVerified());
            responseData.put("emailVerified", status.isEmailVerified());
            responseData.put("stepInstructions", getStepInstructions(status.getCurrentStep()));
            responseData.put("requiredFields", getRequiredFields(status.getCurrentStep()));

            return ResponseEntity.ok(
                ApiResponse.success("registration continuation details", responseData));

        } catch (Exception error) {
            log.error("Error continuing registration: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to continue registration", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "ok");
        healthData.put("timestamp", LocalDateTime.now());
        healthData.put("service", "registration");

        return ResponseEntity.ok(
            ApiResponse.success("registration service is running", healthData));
    }




    private String getNextAction(RegistrationService.StatusResult status) {
        if (status.isExpired()) {
            return "restart_registration";
        }
        if (status.isCompleted()) {
            return "login";
        }

        switch (status.getCurrentStep()) {
            case "phone_verification":
                return "verify_phone";
            case "email_verification":
                return "verify_email";
            case "personal_info":
                return "complete_personal_info";
            case "transaction_pin":
                return "set_transaction_pin";
            case "identity_verification":
                return "submit_identity_documents";
            case "biometric_enrollment":
                return "setup_biometric";
            case "business_verification":
                return "submit_business_info";
            default:
                return "continue_registration";
        }
    }

    private String getNextStep(String currentStep) {
        return "id_verification";
    }

    private String getStepInstructions(String currentStep) {
        return "continue with the registration process";
    }



    private Map<String, Object> getRequiredFields(String currentStep) {
        Map<String, Object> fields = new HashMap<>();

        switch (currentStep) {
            case "phone_verification":
                fields.put("required", Arrays.asList("phone", "code"));
                fields.put("endpoint", "/auth/registration/verify-phone");
                break;
            case "email_verification":
                fields.put("required", Arrays.asList("email", "code"));
                fields.put("endpoint", "/auth/registration/verify-email");
                break;
            case "personal_info":
                fields.put("required", Arrays.asList("registrationId", "firstName", "lastName", "dateOfBirth", "password"));
                fields.put("optional", Arrays.asList("gender", "address"));
                fields.put("endpoint", "/auth/registration/personal-info");
                break;
            case "transaction_pin":
                fields.put("required", Arrays.asList("registrationId", "pin", "confirmPin"));
                fields.put("endpoint", "/auth/registration/transaction-pin");
                break;
            case "identity_verification":
                fields.put("required", Arrays.asList("phone", "idType", "idNumber", "idDocumentFront", "idDocumentBack", "selfiePhoto"));
                fields.put("endpoint", "/auth/registration/kyc-documents");
                break;
            case "biometric_enrollment":
                fields.put("required", Arrays.asList("phone", "biometricEnabled"));
                fields.put("endpoint", "/auth/registration/biometric-setup");
                break;
            case "business_verification":
                fields.put("required", Arrays.asList("phone", "businessName", "businessType", "businessAddress"));
                fields.put("optional", Arrays.asList("businessRegistrationNumber", "businessDocument", "taxCertificate"));
                fields.put("endpoint", "/auth/registration/agent-business-info");
                break;
            default:
                fields.put("required", Arrays.asList());
                fields.put("endpoint", "");
        }

        return fields;
    }
}
