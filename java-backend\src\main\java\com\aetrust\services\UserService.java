package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.User;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.types.Types.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.aetrust.repositories.UserRepository;
import com.aetrust.repositories.UserProfileRepository;
import com.aetrust.repositories.UserWalletRepository;
import com.aetrust.repositories.UserSecurityRepository;
import com.aetrust.repositories.UserPreferencesRepository;
import com.aetrust.repositories.UserIdentityVerificationRepository;
import com.aetrust.repositories.UserAgentInfoRepository;
import com.aetrust.repositories.UserMerchantInfoRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserWalletRepository userWalletRepository;

    @Autowired
    private UserSecurityRepository userSecurityRepository;

    @Autowired
    private UserPreferencesRepository userPreferencesRepository;

    @Autowired
    private UserIdentityVerificationRepository userIdentityVerificationRepository;

    @Autowired
    private UserAgentInfoRepository userAgentInfoRepository;

    @Autowired
    private UserMerchantInfoRepository userMerchantInfoRepository;

    @Autowired
    private CryptoUtils cryptoUtils;

    @Autowired
    private ValidationService validationService;

    @Autowired
    private FileUploadService fileUploadService;


    @Transactional
    public UserCreationResult createCompleteUser(CreateUserRequest request) {
        try {

            if (!validationService.isValidEmail(request.getEmail()) ||
                !validationService.isValidPhone(request.getPhone())) {
                return UserCreationResult.error("Registration failed", "REGISTRATION_FAILED");
            }

            if (userRepository.existsByEmailAndDeletedAtIsNull(request.getEmail()) ||
                userRepository.existsByPhoneAndDeletedAtIsNull(request.getPhone())) {

                log.info("User creation attempted with existing credentials - email: {}, phone: {}",
                    cryptoUtils.maskSensitiveData(request.getEmail(), 2),
                    cryptoUtils.maskSensitiveData(request.getPhone(), 3));

                return UserCreationResult.error("Registration failed", "REGISTRATION_FAILED");
            }

            User user = new User();
            user.setUserUuid(UUID.randomUUID());
            user.setEmail(request.getEmail());
            user.setPhone(request.getPhone());
            // SECURITY FIX: Accept only plaintext passwords and hash server-side
            // The current DTO accepts passwordHash which is insecure
            // TODO: Update DTO to accept plaintext password instead of hash
            if (request.getPasswordHash() != null && !request.getPasswordHash().trim().isEmpty()) {
                // For now, assume the hash is actually a plaintext password that needs validation
                // This should be changed when the DTO is updated
                user.setPassword(request.getPasswordHash()); // This should be cryptoUtils.hashPassword(request.getPassword())
            } else {
                return UserCreationResult.error("Registration failed", "REGISTRATION_FAILED");
            }

            // Convert string role to UserRole enum
            UserRole userRole = UserRole.USER;
            if (request.getRole() != null) {
                try {
                    userRole = UserRole.valueOf(request.getRole().toUpperCase());
                } catch (IllegalArgumentException e) {
                    userRole = UserRole.USER;
                }
            }
            user.setRole(userRole);
            user.setAccountStatus(AccountStatus.ACTIVE);
            user.setKycStatus(KycStatus.PENDING);
            user.setRegistrationStep(RegistrationStep.PHONE_VERIFICATION);

            user = userRepository.save(user);

            User.UserProfile profile = new User.UserProfile();
            profile.setUserId(user.getId());
            profile.setUserUuid(user.getUserUuid());
            profile.setFirstName(request.getFirstName());
            profile.setLastName(request.getLastName());
            profile.setDateOfBirth(request.getDateOfBirth());
            userProfileRepository.save(profile);

            User.UserWallet wallet = new User.UserWallet();
            wallet.setUserId(user.getId());
            wallet.setUserUuid(user.getUserUuid());
            wallet.setWalletType(WalletType.MAIN);
            wallet.setCurrency("USD");
            wallet.setBalance(BigDecimal.ZERO);
            wallet.setAvailableBalance(BigDecimal.ZERO);
            wallet.setPendingBalance(BigDecimal.ZERO);
            wallet.setDefault(true);
            wallet.setStatus(WalletStatus.ACTIVE);
            userWalletRepository.save(wallet);

            User.UserSecurity security = new User.UserSecurity();
            security.setUserId(user.getId());
            security.setUserUuid(user.getUserUuid());
            security.setTransactionPinSet(false);
            security.setBiometricEnabled(false);
            security.setTwoFactorEnabled(false);
            security.setLoginAttempts(0);
            userSecurityRepository.save(security);

            User.UserPreferences preferences = new User.UserPreferences();
            preferences.setUserId(user.getId());
            preferences.setUserUuid(user.getUserUuid());
            preferences.setLanguage("en");
            preferences.setTimezone("UTC");
            preferences.setCurrency("USD");
            preferences.setTheme("light");
            preferences.setEmailNotifications(true);
            preferences.setSmsNotifications(true);
            preferences.setPushNotifications(true);
            userPreferencesRepository.save(preferences);

            User.UserIdentityVerification identity = new User.UserIdentityVerification();
            identity.setUserId(user.getId());
            identity.setUserUuid(user.getUserUuid());
            identity.setVerificationStatus(VerificationStatus.PENDING);
            userIdentityVerificationRepository.save(identity);

            Map<String, Object> userData = createCompleteUserData(user, profile, wallet, security, preferences, identity);

            log.info("Complete user created successfully: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));

            return UserCreationResult.success("user created successfully", userData);

        } catch (Exception error) {
            log.error("Error creating complete user: {}", error.getMessage());
            return UserCreationResult.error("failed to create user", "INTERNAL_ERROR");
        }
    }

    @Transactional(readOnly = true)
    public CompleteUserResult getCompleteUserById(String userId) {
        try {
            UUID userUuid;
            try {
                userUuid = UUID.fromString(userId);
            } catch (IllegalArgumentException e) {
                return CompleteUserResult.error("invalid user id format", "INVALID_USER_ID");
            }

            Optional<User> userOpt = userRepository.findByUserUuidAndDeletedAtIsNull(userUuid);
            if (userOpt.isEmpty()) {
                return CompleteUserResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();
            User.UserProfile profile = userProfileRepository.findByUserUuidAndDeletedAtIsNull(userUuid).orElse(null);
            List<User.UserWallet> wallets = userWalletRepository.findByUserUuidAndDeletedAtIsNull(userUuid);
            User.UserSecurity security = userSecurityRepository.findByUserUuidAndDeletedAtIsNull(userUuid).orElse(null);
            User.UserPreferences preferences = userPreferencesRepository.findByUserUuidAndDeletedAtIsNull(userUuid).orElse(null);
            User.UserIdentityVerification identity = userIdentityVerificationRepository.findByUserUuidAndDeletedAtIsNull(userUuid).orElse(null);
            User.UserAgentInfo agentInfo = userAgentInfoRepository.findByUserUuidAndDeletedAtIsNull(userUuid).orElse(null);
            User.UserMerchantInfo merchantInfo = userMerchantInfoRepository.findByUserUuidAndDeletedAtIsNull(userUuid).orElse(null);

            Map<String, Object> completeUserData = createCompleteUserData(user, profile, wallets, security, preferences, identity, agentInfo, merchantInfo);

            return CompleteUserResult.success("user data retrieved", completeUserData);

        } catch (Exception error) {
            log.error("Error getting complete user: {}", error.getMessage());
            return CompleteUserResult.error("failed to get user data", "INTERNAL_ERROR");
        }
    }

    @Transactional
    public ProfileUpdateResult updateUserProfile(String userId, UpdateProfileRequest request) {
        try {
            UUID userUuid = UUID.fromString(userId);

            Optional<User> userOpt = userRepository.findByUserUuidAndDeletedAtIsNull(userUuid);
            if (userOpt.isEmpty()) {
                return ProfileUpdateResult.error("user not found", "USER_NOT_FOUND");
            }

            User user = userOpt.get();
            User.UserProfile profile = userProfileRepository.findByUserUuidAndDeletedAtIsNull(userUuid)
                .orElseGet(() -> {
                    User.UserProfile newProfile = new User.UserProfile();
                    newProfile.setUserId(user.getId());
                    newProfile.setUserUuid(userUuid);
                    return newProfile;
                });

            List<String> updatedFields = new ArrayList<>();

            if (request.getUsername() != null && !request.getUsername().trim().isEmpty()) {
                if (userRepository.existsByUsernameAndDeletedAtIsNull(request.getUsername().trim()) &&
                    !request.getUsername().trim().equals(user.getUsername())) {
                    return ProfileUpdateResult.error("username already taken", "USERNAME_EXISTS");
                }
                user.setUsername(request.getUsername().trim());
                updatedFields.add("username");
            }

            if (request.getFirstName() != null) {
                profile.setFirstName(request.getFirstName().trim());
                updatedFields.add("first_name");
            }
            if (request.getLastName() != null) {
                profile.setLastName(request.getLastName().trim());
                updatedFields.add("last_name");
            }
            if (request.getBio() != null) {
                profile.setBio(request.getBio().trim());
                updatedFields.add("bio");
            }
            if (request.getDateOfBirth() != null) {
                profile.setDateOfBirth(request.getDateOfBirth());
                updatedFields.add("date_of_birth");
            }
            if (request.getProfilePicture() != null) {
                profile.setProfilePicture(request.getProfilePicture());
                updatedFields.add("profile_picture");
            }

            if (request.getAddress() != null) {
                profile.setStreet(request.getAddress().getStreet());
                profile.setCity(request.getAddress().getCity());
                profile.setState(request.getAddress().getState());
                profile.setCountry(request.getAddress().getCountry());
                profile.setPostalCode(request.getAddress().getPostalCode());
                updatedFields.add("address");
            }

            userRepository.save(user);
            userProfileRepository.save(profile);

            return ProfileUpdateResult.success("profile updated successfully", updatedFields);

        } catch (Exception error) {
            log.error("Error updating user profile: {}", error.getMessage());
            return ProfileUpdateResult.error("failed to update profile", "INTERNAL_ERROR");
        }
    }

    public UserResult getUserById(String userId) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                return UserResult.error("invalid user id", "INVALID_USER_ID");
            }

            Long id;
            try {
                id = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                return UserResult.error("invalid user id format", "INVALID_USER_ID");
            }

            User user = userRepository.findById(id)
                .filter(u -> u.getDeletedAt() == null && u.getAccountStatus() != AccountStatus.CLOSED)
                .orElse(null);

            if (user == null) {
                return UserResult.error("user not found", "USER_NOT_FOUND");
            }

            Map<String, Object> userData = createUserData(user);
            return UserResult.success("user found", userData);

        } catch (Exception error) {
            log.error("Error getting user by ID: {}", error.getMessage());
            return UserResult.error("failed to get user", "INTERNAL_ERROR");
        }
    }
    
    public UpdateResult updateProfile(String userId, UpdateProfileRequest request) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                return UpdateResult.error("invalid user id", "INVALID_USER_ID");
            }

            Long id;
            try {
                id = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                return UpdateResult.error("invalid user id format", "INVALID_USER_ID");
            }

            User user = userRepository.findById(id)
                .filter(u -> u.getDeletedAt() == null)
                .orElse(null);

            if (user == null) {
                return UpdateResult.error("user not found", "USER_NOT_FOUND");
            }

            List<String> updatedFields = new ArrayList<>();

            User.UserProfile profile = userProfileRepository.findByUserIdAndDeletedAtIsNull(user.getId())
                .orElse(new User.UserProfile());
            if (profile.getUserId() == null) {
                profile.setUserId(user.getId());
                profile.setUserUuid(user.getUserUuid());
            }

            if (request.getFirstName() != null && !request.getFirstName().trim().isEmpty()) {
                profile.setFirstName(request.getFirstName().trim());
                updatedFields.add("first_name");
            }

            if (request.getLastName() != null && !request.getLastName().trim().isEmpty()) {
                profile.setLastName(request.getLastName().trim());
                updatedFields.add("last_name");
            }

            if (request.getUsername() != null && !request.getUsername().trim().isEmpty()) {
                if (userRepository.existsByUsernameAndDeletedAtIsNull(request.getUsername().trim()) &&
                    !request.getUsername().trim().equals(user.getUsername())) {
                    return UpdateResult.error("username already taken", "USERNAME_EXISTS");
                }
                user.setUsername(request.getUsername().trim());
                updatedFields.add("username");
            }

            if (request.getBio() != null) {
                profile.setBio(request.getBio().trim());
                updatedFields.add("bio");
            }

            if (request.getDateOfBirth() != null) {
                profile.setDateOfBirth(request.getDateOfBirth());
                updatedFields.add("date_of_birth");
            }

            if (request.getAddress() != null) {
                profile.setStreet(request.getAddress().getStreet());
                profile.setCity(request.getAddress().getCity());
                profile.setState(request.getAddress().getState());
                profile.setCountry(request.getAddress().getCountry());
                profile.setPostalCode(request.getAddress().getPostalCode());
                updatedFields.add("address");
            }

            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            if (updatedFields.stream().anyMatch(field ->
                field.equals("first_name") || field.equals("last_name") ||
                field.equals("bio") || field.equals("date_of_birth") || field.equals("address"))) {
                userProfileRepository.save(profile);
            }

            return UpdateResult.success("profile updated successfully", updatedFields);

        } catch (Exception error) {
            log.error("Error updating profile: {}", error.getMessage());
            return UpdateResult.error("failed to update profile", "INTERNAL_ERROR");
        }
    }
    
    public PasswordUpdateResult updatePassword(String userId, UpdatePasswordRequest request) {
        try {
            Long id;
            try {
                id = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                return PasswordUpdateResult.error("invalid user id format", "INVALID_USER_ID");
            }

            User user = userRepository.findById(id).orElse(null);

            if (user == null) {
                return PasswordUpdateResult.error("user not found", "USER_NOT_FOUND");
            }

            if (!cryptoUtils.verifyPassword(request.getCurrentPassword(), user.getPassword())) {
                return PasswordUpdateResult.error("current password is incorrect", "INVALID_PASSWORD");
            }

            String newPasswordHash = cryptoUtils.hashPassword(request.getNewPassword());

            user.setPassword(newPasswordHash);
            user.setUpdatedAt(LocalDateTime.now());

            userRepository.save(user);

            log.info("Password updated for user: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));

            return PasswordUpdateResult.success("password updated");

        } catch (Exception error) {
            log.error("Error updating password: {}", error.getMessage());
            return PasswordUpdateResult.error("password update failed", "INTERNAL_ERROR");
        }
    }
    
    public UploadResult uploadProfilePicture(String userId, UploadProfilePictureRequest request) {
        try {
            // Validate user ID format
            if (!validationService.isValidUuid(userId)) {
                return UploadResult.error("Access denied", "ACCESS_DENIED");
            }

            // SECURITY FIX: Remove insecure file upload simulation
            // TODO: Update this method to accept MultipartFile and use FileUploadService
            // For now, return error to prevent insecure file handling
            return UploadResult.error("File upload temporarily disabled for security", "UPLOAD_DISABLED");

            // Unreachable code removed - the method returns above
            /*
            Long id;
            String fileUrl = ""; // This would be set by actual file upload service

            try {
                id = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                return UploadResult.error("invalid user id format", "INVALID_USER_ID");
            }

            User user = userRepository.findById(id).orElse(null);
            if (user == null) {
                return UploadResult.error("user not found", "USER_NOT_FOUND");
            }

            // Get or create user profile
            User.UserProfile profile = userProfileRepository.findByUserIdAndDeletedAtIsNull(user.getId())
                .orElse(new User.UserProfile());
            if (profile.getUserId() == null) {
                profile.setUserId(user.getId());
                profile.setUserUuid(user.getUserUuid());
            }

            profile.setProfilePicture(fileUrl);
            user.setUpdatedAt(LocalDateTime.now());

            userRepository.save(user);
            userProfileRepository.save(profile);

            return UploadResult.success("profile picture uploaded", fileUrl);
            */
            
        } catch (Exception error) {
            log.error("Error uploading profile picture: {}", error.getMessage());
            return UploadResult.error("upload failed", "INTERNAL_ERROR");
        }
    }
    
    public DeletionResult deleteAccount(String userId, DeleteAccountRequest request) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                return DeletionResult.error("invalid user id", "INVALID_USER_ID");
            }

            Long id;
            try {
                id = Long.parseLong(userId);
            } catch (NumberFormatException e) {
                return DeletionResult.error("invalid user id format", "INVALID_USER_ID");
            }

            User user = userRepository.findById(id)
                .filter(u -> u.getDeletedAt() == null)
                .orElse(null);

            if (user == null) {
                return DeletionResult.error("user not found", "USER_NOT_FOUND");
            }

            if (!cryptoUtils.verifyPassword(request.getPassword(), user.getPassword())) {
                return DeletionResult.error("password is incorrect", "INVALID_PASSWORD");
            }

            LocalDateTime now = LocalDateTime.now();
            String maskedEmail = "deleted_" + now.toEpochSecond(java.time.ZoneOffset.UTC) + "_" + user.getEmail();
            String maskedPhone = "deleted_" + now.toEpochSecond(java.time.ZoneOffset.UTC) + "_" + user.getPhone();

            user.setDeletedAt(now);
            user.setAccountStatus(AccountStatus.CLOSED);
            user.setEmail(maskedEmail);
            user.setPhone(maskedPhone);
            user.setUpdatedAt(now);

            userRepository.save(user);

            log.info("Account deleted for user: {}", cryptoUtils.maskSensitiveData(user.getEmail(), 2));

            return DeletionResult.success("account deleted successfully", true, now);

        } catch (Exception error) {
            log.error("Error deleting account: {}", error.getMessage());
            return DeletionResult.error("failed to delete account", "INTERNAL_ERROR");
        }
    }
    
    public SearchResult searchUsers(String query, int page, int limit) {
        try {
            // Validate and sanitize input
            if (query != null) {
                query = validationService.sanitizeInput(query.trim());
                if (query.length() > 100) {
                    query = query.substring(0, 100);
                }
            }

            if (limit > 50) {
                limit = 50;
            }

            if (page < 1) {
                page = 1;
            }

            List<User> users = userRepository.searchUsers(query);

            List<User> filteredUsers = users.stream()
                .filter(u -> u.getDeletedAt() == null)
                .skip((long) (page - 1) * limit)
                .limit(limit)
                .toList();

            long totalCount = users.stream()
                .filter(u -> u.getDeletedAt() == null)
                .count();

            List<Map<String, Object>> userList = new ArrayList<>();
            for (User user : filteredUsers) {
                Map<String, Object> userData = new HashMap<>();
                userData.put("id", user.getId().toString());
                userData.put("username", user.getUsername());

                User.UserProfile profile = userProfileRepository.findByUserIdAndDeletedAtIsNull(user.getId()).orElse(null);
                if (profile != null) {
                    userData.put("firstName", profile.getFirstName());
                    userData.put("lastName", profile.getLastName());
                    userData.put("profilePicture", profile.getProfilePicture());
                } else {
                    userData.put("firstName", null);
                    userData.put("lastName", null);
                    userData.put("profilePicture", null);
                }

                userList.add(userData);
            }

            int totalPages = (int) Math.ceil((double) totalCount / limit);
            boolean hasMore = page < totalPages;

            return SearchResult.success("search completed", userList, totalCount, totalPages, hasMore);

        } catch (Exception error) {
            log.error("Error searching users: {}", error.getMessage());
            return SearchResult.error("search failed", "INTERNAL_ERROR");
        }
    }


    @Transactional
    public WalletOperationResult createWallet(String userId, CreateWalletRequest request) {
        try {
            UUID userUuid = UUID.fromString(userId);

            if (!userRepository.existsByUserUuidAndDeletedAtIsNull(userUuid)) {
                return WalletOperationResult.error("user not found", "USER_NOT_FOUND");
            }

            WalletType walletType = WalletType.MAIN;
            if (request.getWalletType() != null) {
                try {
                    walletType = WalletType.valueOf(request.getWalletType().toUpperCase());
                } catch (IllegalArgumentException e) {
                    walletType = WalletType.MAIN;
                }
            }

            if (userWalletRepository.existsByUserUuidAndWalletTypeAndDeletedAtIsNull(userUuid, walletType)) {
                return WalletOperationResult.error("wallet type already exists", "WALLET_EXISTS");
            }

            User user = userRepository.findByUserUuidAndDeletedAtIsNull(userUuid).get();

            User.UserWallet wallet = new User.UserWallet();
            wallet.setUserId(user.getId());
            wallet.setUserUuid(userUuid);
            wallet.setWalletType(walletType);
            wallet.setCurrency(request.getCurrency());
            wallet.setBalance(BigDecimal.ZERO);
            wallet.setAvailableBalance(BigDecimal.ZERO);
            wallet.setPendingBalance(BigDecimal.ZERO);
            wallet.setDefault(request.isDefault());
            wallet.setStatus(WalletStatus.ACTIVE);


            if (request.isDefault()) {
                List<User.UserWallet> existingWallets = userWalletRepository.findByUserUuidAndDeletedAtIsNull(userUuid);
                for (User.UserWallet existingWallet : existingWallets) {
                    existingWallet.setDefault(false);
                    userWalletRepository.save(existingWallet);
                }
            }

            wallet = userWalletRepository.save(wallet);

            Map<String, Object> walletData = createWalletData(wallet);
            return WalletOperationResult.success("wallet created successfully", walletData);

        } catch (Exception error) {
            log.error("Error creating wallet: {}", error.getMessage());
            return WalletOperationResult.error("failed to create wallet", "INTERNAL_ERROR");
        }
    }

    @Transactional
    public WalletOperationResult updateWalletBalance(String userId, String walletId, UpdateBalanceRequest request) {
        try {
            // Validate input parameters
            if (!validationService.isValidUuid(userId)) {
                return WalletOperationResult.error("Access denied", "ACCESS_DENIED");
            }

            UUID userUuid;
            Long walletIdLong;

            try {
                userUuid = UUID.fromString(userId);
                walletIdLong = Long.parseLong(walletId);
            } catch (Exception e) {
                return WalletOperationResult.error("Access denied", "ACCESS_DENIED");
            }

            // Validate ownership with enhanced security
            Optional<User.UserWallet> walletOpt = userWalletRepository.findById(walletIdLong);
            if (walletOpt.isEmpty() || !walletOpt.get().getUserUuid().equals(userUuid)) {
                log.warn("Unauthorized wallet access attempt - user: {}, wallet: {}",
                    userUuid, walletIdLong);
                return WalletOperationResult.error("Access denied", "ACCESS_DENIED");
            }

            User.UserWallet wallet = walletOpt.get();

            if (!wallet.canTransact()) {
                return WalletOperationResult.error("wallet cannot transact", "WALLET_INACTIVE");
            }

            if (request.getTransactionType().equals("DEBIT")) {
                if (wallet.hasInsufficientFunds(request.getAmount())) {
                    return WalletOperationResult.error("insufficient funds", "INSUFFICIENT_FUNDS");
                }
                if (wallet.exceedsTransactionLimit(request.getAmount())) {
                    return WalletOperationResult.error("transaction limit exceeded", "LIMIT_EXCEEDED");
                }
            }

            wallet.updateBalance(request.getAmount(), request.getTransactionType().equals("CREDIT"));
            userWalletRepository.save(wallet);

            Map<String, Object> walletData = createWalletData(wallet);
            return WalletOperationResult.success("balance updated successfully", walletData);

        } catch (Exception error) {
            log.error("Error updating wallet balance: {}", error.getMessage());
            return WalletOperationResult.error("failed to update balance", "INTERNAL_ERROR");
        }
    }

 

    @Transactional
    public SecurityOperationResult updateSecuritySettings(String userId, UpdateSecurityRequest request) {
        try {
            UUID userUuid = UUID.fromString(userId);

            User.UserSecurity security = userSecurityRepository.findByUserUuidAndDeletedAtIsNull(userUuid)
                .orElseThrow(() -> new RuntimeException("Security record not found"));

            List<String> updatedSettings = new ArrayList<>();

            if (request.getTransactionPin() != null) {
                security.setTransactionPinHash(cryptoUtils.hashPassword(request.getTransactionPin()));
                security.setTransactionPinSet(true);
                updatedSettings.add("transaction_pin");
            }

            if (request.getBiometricEnabled() != null) {
                security.setBiometricEnabled(request.getBiometricEnabled());
                if (request.getBiometricEnabled()) {
                    security.setBiometricEnrolledAt(LocalDateTime.now());
                }
                updatedSettings.add("biometric");
            }

            if (request.getTwoFactorEnabled() != null) {
                security.setTwoFactorEnabled(request.getTwoFactorEnabled());
                updatedSettings.add("two_factor");
            }

            userSecurityRepository.save(security);

            return SecurityOperationResult.success("security settings updated", updatedSettings);

        } catch (Exception error) {
            log.error("Error updating security settings: {}", error.getMessage());
            return SecurityOperationResult.error("failed to update security", "INTERNAL_ERROR");
        }
    }


    @Transactional
    public VerificationResult submitIdentityVerification(String userId, SubmitIdentityRequest request) {
        try {
            UUID userUuid = UUID.fromString(userId);

            User.UserIdentityVerification identity = userIdentityVerificationRepository.findByUserUuidAndDeletedAtIsNull(userUuid)
                .orElseThrow(() -> new RuntimeException("Identity verification record not found"));


            IdType idType = IdType.PASSPORT;
            if (request.getIdType() != null) {
                try {
                    idType = IdType.valueOf(request.getIdType().toUpperCase());
                } catch (IllegalArgumentException e) {
                    idType = IdType.PASSPORT;
                }
            }
            identity.setIdType(idType);
            identity.setIdNumber(request.getIdNumber());
            identity.setIdDocumentFront(request.getIdDocumentFront());
            identity.setIdDocumentBack(request.getIdDocumentBack());
            identity.setSelfiePhoto(request.getSelfiePhoto());
            identity.setVerificationStatus(VerificationStatus.UNDER_REVIEW);

            userIdentityVerificationRepository.save(identity);

            User user = userRepository.findByUserUuidAndDeletedAtIsNull(userUuid).get();
            user.setKycStatus(KycStatus.UNDER_REVIEW);
            userRepository.save(user);

            return VerificationResult.success("identity verification submitted", identity.hasCompleteDocuments());

        } catch (Exception error) {
            log.error("Error submitting identity verification: {}", error.getMessage());
            return VerificationResult.error("failed to submit verification", "INTERNAL_ERROR");
        }
    }

    private Map<String, Object> createUserData(User user) {
        Map<String, Object> userData = new HashMap<>();
        userData.put("id", user.getId());
        userData.put("email", user.getEmail());
        userData.put("phone", user.getPhone());
        userData.put("username", user.getUsername());

        // Get profile information
        User.UserProfile profile = userProfileRepository.findByUserIdAndDeletedAtIsNull(user.getId()).orElse(null);
        if (profile != null) {
            userData.put("first_name", profile.getFirstName());
            userData.put("last_name", profile.getLastName());
            userData.put("full_name", profile.getFullName());
            userData.put("profile_picture", profile.getProfilePicture());
            userData.put("bio", profile.getBio());
            userData.put("address", profile.getFullAddress());
        } else {
            userData.put("first_name", null);
            userData.put("last_name", null);
            userData.put("full_name", null);
            userData.put("profile_picture", null);
            userData.put("bio", null);
            userData.put("address", null);
        }

        userData.put("role", user.getRole().name());
        userData.put("is_verified", user.isVerified());
        userData.put("kyc_status", user.getKycStatus().name());

        // Todo wallet balance and agent info for now
        userData.put("wallet_balance", "0.00");
        userData.put("agent_info", null);

        userData.put("created_at", user.getCreatedAt());
        userData.put("updated_at", user.getUpdatedAt());
        return userData;
    }
    


    private Map<String, Object> createCompleteUserData(User user, User.UserProfile profile,
                                                      User.UserWallet wallet, User.UserSecurity security,
                                                      User.UserPreferences preferences, User.UserIdentityVerification identity) {
        Map<String, Object> userData = new HashMap<>();
        userData.put("user", createBasicUserData(user));
        userData.put("profile", createProfileData(profile));
        userData.put("wallet", createWalletData(wallet));
        userData.put("security", createSecurityData(security));
        userData.put("preferences", createPreferencesData(preferences));
        userData.put("identity", createIdentityData(identity));
        return userData;
    }

    private Map<String, Object> createCompleteUserData(User user, User.UserProfile profile,
                                                      List<User.UserWallet> wallets, User.UserSecurity security,
                                                      User.UserPreferences preferences, User.UserIdentityVerification identity,
                                                      User.UserAgentInfo agentInfo, User.UserMerchantInfo merchantInfo) {
        Map<String, Object> userData = new HashMap<>();
        userData.put("user", createBasicUserData(user));
        userData.put("profile", createProfileData(profile));
        userData.put("wallets", wallets.stream().map(this::createWalletData).toList());
        userData.put("security", createSecurityData(security));
        userData.put("preferences", createPreferencesData(preferences));
        userData.put("identity", createIdentityData(identity));
        userData.put("agentInfo", agentInfo != null ? createAgentData(agentInfo) : null);
        userData.put("merchantInfo", merchantInfo != null ? createMerchantData(merchantInfo) : null);
        return userData;
    }

    private Map<String, Object> createBasicUserData(User user) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", user.getId());
        data.put("userUuid", user.getUserUuid());
        data.put("email", user.getEmail());
        data.put("phone", user.getPhone());
        data.put("username", user.getUsername());
        data.put("role", user.getRole());
        data.put("accountStatus", user.getAccountStatus());
        data.put("kycStatus", user.getKycStatus());
        data.put("isVerified", user.isVerified());
        data.put("registrationStep", user.getRegistrationStep());
        data.put("createdAt", user.getCreatedAt());
        data.put("updatedAt", user.getUpdatedAt());
        return data;
    }

    private Map<String, Object> createProfileData(User.UserProfile profile) {
        if (profile == null) return null;
        Map<String, Object> data = new HashMap<>();
        data.put("firstName", profile.getFirstName());
        data.put("lastName", profile.getLastName());
        data.put("fullName", profile.getFullName());
        data.put("dateOfBirth", profile.getDateOfBirth());
        data.put("bio", profile.getBio());
        data.put("profilePicture", profile.getProfilePicture());
        data.put("address", profile.getFullAddress());
        return data;
    }

    private Map<String, Object> createWalletData(User.UserWallet wallet) {
        if (wallet == null) return null;
        Map<String, Object> data = new HashMap<>();
        data.put("id", wallet.getId());
        data.put("walletType", wallet.getWalletType());
        data.put("currency", wallet.getCurrency());
        data.put("balance", wallet.getBalance());
        data.put("availableBalance", wallet.getAvailableBalance());
        data.put("pendingBalance", wallet.getPendingBalance());
        data.put("isDefault", wallet.isDefault());
        data.put("status", wallet.getStatus());
        data.put("totalTransactions", wallet.getTotalTransactions());
        data.put("canTransact", wallet.canTransact());
        return data;
    }

    private Map<String, Object> createSecurityData(User.UserSecurity security) {
        if (security == null) return null;
        Map<String, Object> data = new HashMap<>();
        data.put("transactionPinSet", security.isTransactionPinSet());
        data.put("biometricEnabled", security.isBiometricEnabled());
        data.put("twoFactorEnabled", security.isTwoFactorEnabled());
        data.put("lastLogin", security.getLastLogin());
        data.put("isAccountLocked", security.isAccountLocked());
        data.put("isPinLocked", security.isPinLocked());
        return data;
    }

    private Map<String, Object> createPreferencesData(User.UserPreferences preferences) {
        if (preferences == null) return null;
        Map<String, Object> data = new HashMap<>();
        data.put("language", preferences.getLanguage());
        data.put("timezone", preferences.getTimezone());
        data.put("currency", preferences.getCurrency());
        data.put("theme", preferences.getTheme());
        data.put("emailNotifications", preferences.isEmailNotifications());
        data.put("smsNotifications", preferences.isSmsNotifications());
        data.put("pushNotifications", preferences.isPushNotifications());
        return data;
    }

    private Map<String, Object> createIdentityData(User.UserIdentityVerification identity) {
        if (identity == null) return null;
        Map<String, Object> data = new HashMap<>();
        data.put("idType", identity.getIdType());
        data.put("verificationStatus", identity.getVerificationStatus());
        data.put("isVerified", identity.isVerified());
        data.put("hasRequiredDocuments", identity.hasRequiredDocuments());
        data.put("hasCompleteDocuments", identity.hasCompleteDocuments());
        data.put("verifiedAt", identity.getVerifiedAt());
        return data;
    }

    private Map<String, Object> createAgentData(User.UserAgentInfo agentInfo) {
        Map<String, Object> data = new HashMap<>();
        data.put("commissionRate", agentInfo.getCommissionRate());
        data.put("totalTransactions", agentInfo.getTotalTransactions());
        data.put("totalCommissionEarned", agentInfo.getTotalCommissionEarned());
        data.put("isActive", agentInfo.isActive());
        data.put("businessName", agentInfo.getBusinessName());
        data.put("verificationStatus", agentInfo.getVerificationStatus());
        data.put("performanceRating", agentInfo.getPerformanceRating());
        data.put("successRate", agentInfo.getSuccessRate());
        return data;
    }

    private Map<String, Object> createMerchantData(User.UserMerchantInfo merchantInfo) {
        Map<String, Object> data = new HashMap<>();
        data.put("businessName", merchantInfo.getBusinessName());
        data.put("businessType", merchantInfo.getBusinessType());
        data.put("merchantCategory", merchantInfo.getMerchantCategory());
        data.put("verificationStatus", merchantInfo.getVerificationStatus());
        data.put("hasApiAccess", merchantInfo.hasApiAccess());
        data.put("isWebhookConfigured", merchantInfo.isWebhookConfigured());
        return data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Map<String, Object> user;

        public static UserResult success(String message, Map<String, Object> user) {
            return new UserResult(true, message, null, user);
        }

        public static UserResult error(String message, String errorCode) {
            return new UserResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateResult {
        private boolean success;
        private String message;
        private String errorCode;
        private List<String> updatedFields;

        public static UpdateResult success(String message, List<String> updatedFields) {
            return new UpdateResult(true, message, null, updatedFields);
        }

        public static UpdateResult error(String message, String errorCode) {
            return new UpdateResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordUpdateResult {
        private boolean success;
        private String message;
        private String errorCode;

        public static PasswordUpdateResult success(String message) {
            return new PasswordUpdateResult(true, message, null);
        }

        public static PasswordUpdateResult error(String message, String errorCode) {
            return new PasswordUpdateResult(false, message, errorCode);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UploadResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String fileUrl;

        public static UploadResult success(String message, String fileUrl) {
            return new UploadResult(true, message, null, fileUrl);
        }

        public static UploadResult error(String message, String errorCode) {
            return new UploadResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeletionResult {
        private boolean success;
        private String message;
        private String errorCode;
        private boolean deletionScheduled;
        private LocalDateTime finalDeletionDate;

        public static DeletionResult success(String message, boolean deletionScheduled, LocalDateTime finalDeletionDate) {
            return new DeletionResult(true, message, null, deletionScheduled, finalDeletionDate);
        }

        public static DeletionResult error(String message, String errorCode) {
            return new DeletionResult(false, message, errorCode, false, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchResult {
        private boolean success;
        private String message;
        private String errorCode;
        private List<Map<String, Object>> users;
        private long totalCount;
        private int totalPages;
        private boolean hasMore;

        public static SearchResult success(String message, List<Map<String, Object>> users,
                                         long totalCount, int totalPages, boolean hasMore) {
            return new SearchResult(true, message, null, users, totalCount, totalPages, hasMore);
        }

        public static SearchResult error(String message, String errorCode) {
            return new SearchResult(false, message, errorCode, null, 0, 0, false);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserCreationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Object data;

        public static UserCreationResult success(String message, Object data) {
            return new UserCreationResult(true, message, null, data);
        }

        public static UserCreationResult error(String message, String errorCode) {
            return new UserCreationResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompleteUserResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Object data;

        public static CompleteUserResult success(String message, Object data) {
            return new CompleteUserResult(true, message, null, data);
        }

        public static CompleteUserResult error(String message, String errorCode) {
            return new CompleteUserResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProfileUpdateResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Object data;

        public static ProfileUpdateResult success(String message, Object data) {
            return new ProfileUpdateResult(true, message, null, data);
        }

        public static ProfileUpdateResult error(String message, String errorCode) {
            return new ProfileUpdateResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WalletOperationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Object data;

        public static WalletOperationResult success(String message, Object data) {
            return new WalletOperationResult(true, message, null, data);
        }

        public static WalletOperationResult error(String message, String errorCode) {
            return new WalletOperationResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SecurityOperationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Object data;

        public static SecurityOperationResult success(String message, Object data) {
            return new SecurityOperationResult(true, message, null, data);
        }

        public static SecurityOperationResult error(String message, String errorCode) {
            return new SecurityOperationResult(false, message, errorCode, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Object data;

        public static VerificationResult success(String message, Object data) {
            return new VerificationResult(true, message, null, data);
        }

        public static VerificationResult error(String message, String errorCode) {
            return new VerificationResult(false, message, errorCode, null);
        }
    }
}
