import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { SecurityMiddleware } from '../middleware/security.middleware';
import * as secureAuthController from '../controllers/secure-auth.controller';

export async function secureAuthRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // public routes
  fastify.post('/login', {
    preHandler: [
      SecurityMiddleware.requestId,
      SecurityMiddleware.rateLimiting,
      SecurityMiddleware.securityHeaders
    ]
  }, secureAuthController.secureLogin as any);

  fastify.post('/refresh', {
    preHandler: [
      SecurityMiddleware.requestId,
      SecurityMiddleware.rateLimiting
    ]
  }, secureAuthController.secureRefreshToken as any);

  // protected routes
  fastify.post('/logout', {
    preHandler: [
      SecurityMiddleware.requestId,
      AuthMiddleware.authenticateRequest
    ]
  }, secureAuthController.secureLogout as any);

  fastify.post('/2fa/setup', {
    preHandler: [
      SecurityMiddleware.requestId,
      AuthMiddleware.authenticateRequest,
      SecurityMiddleware.securityCheck
    ]
  }, secureAuthController.secureSetup2FA as any);

  fastify.post('/2fa/verify', {
    preHandler: [
      SecurityMiddleware.requestId,
      AuthMiddleware.authenticateRequest
    ]
  }, secureAuthController.secureVerify2FA as any);

  fastify.delete('/2fa', {
    preHandler: [
      SecurityMiddleware.requestId,
      AuthMiddleware.authenticateRequest,
      SecurityMiddleware.securityCheck
    ]
  }, secureAuthController.secureDisable2FA as any);

  // health check
  fastify.get('/health', secureAuthController.secureAuthHealth as any);
}
