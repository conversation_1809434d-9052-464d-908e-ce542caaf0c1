package com.aetrust.repositories;

import com.aetrust.models.SystemConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SystemConfigRepository extends JpaRepository<SystemConfig, Long> {
    
    Optional<SystemConfig> findByConfigKey(String configKey);
    Optional<SystemConfig> findByConfigKeyAndIsActive(String configKey, boolean isActive);
    List<SystemConfig> findByIsActiveTrue();
    List<SystemConfig> findByCategoryAndIsActive(String category, boolean isActive);
    List<SystemConfig> findByConfigTypeAndIsActive(String configType, boolean isActive);
    boolean existsByConfigKey(String configKey);

    @Query("SELECT sc FROM SystemConfig sc WHERE sc.configKey LIKE %:pattern% AND sc.isActive = true")
    List<SystemConfig> findByConfigKeyPattern(@Param("pattern") String pattern);
    
    @Query("SELECT sc FROM SystemConfig sc WHERE " +
           "(LOWER(sc.configKey) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(sc.description) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND sc.isActive = true")
    List<SystemConfig> searchConfigs(@Param("search") String search);
}
