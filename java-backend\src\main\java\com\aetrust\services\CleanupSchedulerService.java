package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CleanupSchedulerService {
    
    @Autowired
    private RegistrationService registrationService;
    
    /**
     * Clean up expired verification codes every 30 minutes
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30 minutes
    public void cleanupExpiredVerificationCodes() {
        try {
            log.debug("Starting cleanup of expired verification codes");
            int deletedCount = registrationService.cleanupExpiredVerificationCodes();
            
            if (deletedCount > 0) {
                log.info("Cleanup completed: {} expired verification codes removed", deletedCount);
            }
        } catch (Exception e) {
            log.error("Error during verification code cleanup", e);
        }
    }
    
    /**
     * Clean up old used verification codes every 6 hours
     */
    @Scheduled(fixedRate = 6 * 60 * 60 * 1000) // 6 hours
    public void cleanupOldUsedVerificationCodes() {
        try {
            log.debug("Starting cleanup of old used verification codes");
            int deletedCount = registrationService.cleanupOldUsedVerificationCodes();
            
            if (deletedCount > 0) {
                log.info("Cleanup completed: {} old used verification codes removed", deletedCount);
            }
        } catch (Exception e) {
            log.error("Error during old verification code cleanup", e);
        }
    }
    
    /**
     * Clean up expired registration sessions every hour
     */

    
    /**
     * Comprehensive cleanup that runs daily at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * *") // Daily at 2 AM
    public void dailyCleanup() {
        try {
            log.info("Starting daily comprehensive cleanup");
            
            // Clean up expired verification codes
            int expiredCodes = registrationService.cleanupExpiredVerificationCodes();
            
            // Clean up old used verification codes
            int oldUsedCodes = registrationService.cleanupOldUsedVerificationCodes();
            
            log.info("Daily cleanup completed: {} expired codes, {} old used codes removed",
                expiredCodes, oldUsedCodes);
                
        } catch (Exception e) {
            log.error("Error during daily cleanup", e);
        }
    }
}
