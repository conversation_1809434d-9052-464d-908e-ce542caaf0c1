package com.aetrust.validation;

import com.aetrust.validation.Validation.PlatformValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class PlatformValidatorTest {

    private PlatformValidator validator;

    @BeforeEach
    void setUp() {
        validator = new PlatformValidator();
    }

    @ParameterizedTest
    @ValueSource(strings = {"web", "app", "mobile_web", "WEB", "APP", "MOBILE_WEB", " web ", " app ", " mobile_web "})
    void testValidPlatforms(String platform) {
        assertTrue(validator.isValid(platform, null), 
            "Platform '" + platform + "' should be valid");
    }

    @ParameterizedTest
    @ValueSource(strings = {"web2", "mobile", "desktop", "ios", "android", "invalid", "", " ", "web_app"})
    void testInvalidPlatforms(String platform) {
        assertFalse(validator.isValid(platform, null), 
            "Platform '" + platform + "' should be invalid");
    }

    @Test
    void testNullPlatform() {
        assertFalse(validator.isValid(null, null), 
            "Null platform should be invalid");
    }

    @Test
    void testEmptyPlatform() {
        assertFalse(validator.isValid("", null), 
            "Empty platform should be invalid");
    }

    @Test
    void testWhitespacePlatform() {
        assertFalse(validator.isValid("   ", null), 
            "Whitespace-only platform should be invalid");
    }
}
