package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DatabaseService {
    
    private final DataSource dataSource;
    private final JdbcTemplate jdbcTemplate;
    
    @Value("${spring.datasource.hikari.connection-timeout:30000}")
    private long connectionTimeout;
    
    @Value("${spring.datasource.hikari.validation-timeout:5000}")
    private long validationTimeout;
    
    @Autowired
    public DatabaseService(DataSource dataSource, JdbcTemplate jdbcTemplate) {
        this.dataSource = dataSource;
        this.jdbcTemplate = jdbcTemplate;
        log.info("DatabaseService initialized with connection pool");
    }
    
    // health check method
    public boolean isHealthy() {
        try {
            return executeWithTimeout(() -> {
                try (Connection conn = getConnection()) {
                    return conn.isValid((int) TimeUnit.MILLISECONDS.toSeconds(validationTimeout));
                }
            }, 5000);
        } catch (Exception e) {
            log.error("Database health check failed", e);
            return false;
        }
    }
    
    // get connection with proper error handling
    public Connection getConnection() throws SQLException {
        try {
            Connection conn = dataSource.getConnection();
            if (conn == null || conn.isClosed()) {
                throw new SQLException("Failed to obtain database connection");
            }
            return conn;
        } catch (SQLException e) {
            log.error("Failed to get database connection", e);
            throw new SQLException("Database connection failed: " + e.getMessage(), e);
        }
    }
    
    // execute query with retry logic and circuit breaker pattern
    @Transactional(readOnly = true)
    public <T> List<T> executeQuery(String sql, RowMapper<T> rowMapper, Object... params) {
        return executeWithRetry(() -> {
            try {
                if (params.length > 0) {
                    return jdbcTemplate.query(sql, rowMapper, params);
                } else {
                    return jdbcTemplate.query(sql, rowMapper);
                }
            } catch (DataAccessException e) {
                log.error("Query execution failed: {}", sql, e);
                throw new RuntimeException("Database query failed", e);
            }
        }, 3);
    }
    
    // execute single query with optional result
    @Transactional(readOnly = true)
    public <T> Optional<T> executeQueryForObject(String sql, RowMapper<T> rowMapper, Object... params) {
        try {
            List<T> results = executeQuery(sql, rowMapper, params);
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            log.error("Single query execution failed: {}", sql, e);
            return Optional.empty();
        }
    }
    
    // execute update/insert/delete with transaction support
    @Transactional
    public int executeUpdate(String sql, Object... params) {
        return executeWithRetry(() -> {
            try {
                return jdbcTemplate.update(sql, params);
            } catch (DataAccessException e) {
                log.error("Update execution failed: {}", sql, e);
                throw new RuntimeException("Database update failed", e);
            }
        }, 3);
    }
    
    // batch operations for better performance
    @Transactional
    public int[] executeBatch(String sql, List<Object[]> batchParams) {
        return executeWithRetry(() -> {
            try {
                return jdbcTemplate.batchUpdate(sql, batchParams);
            } catch (DataAccessException e) {
                log.error("Batch execution failed: {}", sql, e);
                throw new RuntimeException("Database batch operation failed", e);
            }
        }, 2);
    }
    
    // raw jdbc operations for complex scenarios
    public <T> T executeWithConnection(ConnectionCallback<T> callback) throws SQLException {
        try (Connection conn = getConnection()) {
            return callback.execute(conn);
        } catch (SQLException e) {
            String errorCode = e.getSQLState();
            log.error("Raw JDBC operation failed with error code: {}", errorCode, e);
            
            // postgresql error codes
            switch (errorCode) {
                case "08000": // connection_exception
                case "08003": // connection_does_not_exist
                case "08006": // connection_failure
                    log.warn("Connection error detected, may need retry");
                    break;
                case "42601": // syntax_error
                case "42501": // insufficient_privilege
                case "42P01": // undefined_table
                    log.error("SQL syntax or permission error: {}", e.getMessage());
                    break;
                case "23505": // unique_violation
                    log.warn("Unique constraint violation: {}", e.getMessage());
                    break;
                case "23503": // foreign_key_violation
                    log.warn("Foreign key constraint violation: {}", e.getMessage());
                    break;
                default:
                    log.error("Unhandled SQL error code: {}", errorCode);
            }
            throw e;
        }
    }
    
    // prepared statement operations
    public <T> T executeWithPreparedStatement(String sql, PreparedStatementCallback<T> callback, Object... params) throws SQLException {
        return executeWithConnection(conn -> {
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                // set parameters
                for (int i = 0; i < params.length; i++) {
                    stmt.setObject(i + 1, params[i]);
                }
                return callback.execute(stmt);
            }
        });
    }
    
    // transaction management
    @Transactional
    public <T> T executeInTransaction(TransactionCallback<T> callback) {
        try {
            return callback.execute();
        } catch (Exception e) {
            log.error("Transaction execution failed", e);
            throw new RuntimeException("Transaction failed", e);
        }
    }
    
    // utility method for checking table existence
    public boolean tableExists(String tableName) {
        try {
            String sql = "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = ?)";
            return executeQueryForObject(sql, (rs, rowNum) -> rs.getBoolean(1), tableName)
                    .orElse(false);
        } catch (Exception e) {
            log.error("Failed to check table existence: {}", tableName, e);
            return false;
        }
    }
    
    // get database metadata
    public Map<String, Object> getDatabaseInfo() {
        try {
            return executeWithConnection(conn -> {
                DatabaseMetaData metaData = conn.getMetaData();
                return Map.of(
                    "database_product_name", metaData.getDatabaseProductName(),
                    "database_product_version", metaData.getDatabaseProductVersion(),
                    "driver_name", metaData.getDriverName(),
                    "driver_version", metaData.getDriverVersion(),
                    "max_connections", metaData.getMaxConnections(),
                    "url", metaData.getURL()
                );
            });
        } catch (Exception e) {
            log.error("Failed to get database info", e);
            return Map.of("error", e.getMessage());
        }
    }
    
    // connection pool stats
    public Map<String, Object> getConnectionPoolStats() {
        try {
            if (dataSource instanceof com.zaxxer.hikari.HikariDataSource) {
                com.zaxxer.hikari.HikariDataSource hikariDS = (com.zaxxer.hikari.HikariDataSource) dataSource;
                com.zaxxer.hikari.HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                
                return Map.of(
                    "active_connections", poolBean.getActiveConnections(),
                    "idle_connections", poolBean.getIdleConnections(),
                    "total_connections", poolBean.getTotalConnections(),
                    "threads_awaiting_connection", poolBean.getThreadsAwaitingConnection()
                );
            }
            return Map.of("message", "Connection pool stats not available");
        } catch (Exception e) {
            log.error("Failed to get connection pool stats", e);
            return Map.of("error", e.getMessage());
        }
    }
    
    // helper methods
    private <T> T executeWithRetry(RetryCallback<T> callback, int maxAttempts) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return callback.execute();
            } catch (Exception e) {
                lastException = e;
                if (attempt < maxAttempts) {
                    log.warn("Database operation failed, attempt {}/{}: {}", attempt, maxAttempts, e.getMessage());
                    try {
                        Thread.sleep(1000 * attempt); // exponential backoff
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        log.error("Database operation failed after {} attempts", maxAttempts, lastException);
        throw new RuntimeException("Database operation failed after retries", lastException);
    }
    
    private <T> T executeWithTimeout(TimeoutCallback<T> callback, long timeoutMs) throws Exception {
        CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
            try {
                return callback.execute();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        return future.get(timeoutMs, TimeUnit.MILLISECONDS);
    }
    
    // functional interfaces
    @FunctionalInterface
    public interface ConnectionCallback<T> {
        T execute(Connection conn) throws SQLException;
    }
    
    @FunctionalInterface
    public interface PreparedStatementCallback<T> {
        T execute(PreparedStatement stmt) throws SQLException;
    }
    
    @FunctionalInterface
    public interface TransactionCallback<T> {
        T execute() throws Exception;
    }
    
    @FunctionalInterface
    private interface RetryCallback<T> {
        T execute() throws Exception;
    }
    
    @FunctionalInterface
    private interface TimeoutCallback<T> {
        T execute() throws Exception;
    }
}
