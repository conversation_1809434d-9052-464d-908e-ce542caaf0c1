import { Notification, INotification } from '../models/notification.model';
import { User } from '../models/user.model';
import { NotificationType, NotificationStatus } from '../types';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export interface NotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  category: 'transaction' | 'security' | 'account' | 'loan' | 'promotion' | 'system';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  data?: any;
  scheduledFor?: Date;
  expiresAt?: Date;
}

export interface TransactionNotificationData {
  userId: string;
  transactionType: string;
  amount: number;
  currency: string;
  recipientName?: string;
  transactionRef: string;
  balance?: number;
}

export class NotificationService {
 
  static async createNotification(data: NotificationData): Promise<INotification> {
    try {
      const notification = new Notification({
        user_id: data.userId,
        type: data.type,
        title: data.title,
        message: data.message,
        category: data.category,
        priority: data.priority || 'medium',
        data: data.data || {},
        scheduled_for: data.scheduledFor,
        expires_at: data.expiresAt
      });

      await notification.save();

      //  if not scheduled
      if (!data.scheduledFor || data.scheduledFor <= new Date()) {
        await this.sendNotification(notification);
      }

      return notification;
    } catch (error: any) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Send notification based on type
   */
  static async sendNotification(notification: INotification): Promise<void> {
    try {
      const user = await User.findById(notification.user_id);
      if (!user) {
        await (notification as any).markAsFailed('User not found');
        return;
      }

      notification.status = NotificationStatus.SENT;
      notification.last_attempt_at = new Date();
      notification.delivery_attempts += 1;

      let success = false;
      let externalId: string | undefined;

      switch (notification.type) {
        case NotificationType.EMAIL:
          if (user.preferences?.notifications?.email) {
            const { CommunicationService } = await import('./communication.service');
            const result = await CommunicationService.sendEmail({
              to: user.email,
              subject: notification.title,
              html: this.formatEmailContent(notification)
            });
            success = result.success;
            externalId = result.messageId;
          }
          break;

        case NotificationType.SMS:
          if (user.preferences?.notifications?.sms) {
            const { CommunicationService } = await import('./communication.service');
            const result = await CommunicationService.sendSms({
              to: user.phone,
              message: `${notification.title}: ${notification.message}`,
              userId: user._id.toString()
            });
            success = result.success;
            externalId = result.messageId;
          }
          break;

        case NotificationType.PUSH:
          if (user.preferences?.notifications?.push) {
            success = true;
          }
          break;

        case NotificationType.IN_APP:
          // In-app notifications stored in db only
          success = true;
          break;

        default:
          success = false;
      }

      if (success) {
        await (notification as any).markAsDelivered(externalId);
      } else {
        await (notification as any).markAsFailed('Delivery failed');
      }

    } catch (error: any) {
      logger.error('Error sending notification:', error);
      await (notification as any).markAsFailed(error.message);
    }
  }

 
  static async sendTransactionNotification(data: TransactionNotificationData): Promise<void> {
    try {
      const user = await User.findById(data.userId);
      if (!user) return;

      const title = `Transaction ${data.transactionType}`;
      const message = data.recipientName 
        ? `${data.transactionType} of ${data.currency} ${data.amount.toFixed(2)} to ${data.recipientName} completed successfully.`
        : `${data.transactionType} of ${data.currency} ${data.amount.toFixed(2)} completed successfully.`;

      // Create in-app notification
      await this.createNotification({
        userId: data.userId,
        type: NotificationType.IN_APP,
        title,
        message,
        category: 'transaction',
        priority: 'medium',
        data: {
          transactionRef: data.transactionRef,
          amount: data.amount,
          currency: data.currency,
          balance: data.balance,
          type: data.transactionType
        }
      });

      // Send SMS and email if enabled
      if (user.preferences?.notifications?.sms || user.preferences?.notifications?.email) {
        const { CommunicationService } = await import('./communication.service');
        await CommunicationService.sendTransactionNotification(
          user.phone,
          user.email,
          data.transactionType,
          data.amount,
          data.currency,
          user.first_name
        );
      }

    } catch (error: any) {
      logger.error('Error sending transaction notification:', error);
    }
  }


  static async getUserNotifications(
    userId: string,
    options: {
      category?: string;
      type?: NotificationType;
      read?: boolean;
      limit?: number;
      skip?: number;
    } = {}
  ): Promise<{
    notifications: INotification[];
    total: number;
    unreadCount: number;
  }> {
    try {
      const notifications = await (Notification as any).findUserNotifications(userId, options);
      const total = await Notification.countDocuments({ user_id: userId });
      const unreadCount = await (Notification as any).getUnreadCount(userId);

      return {
        notifications,
        total,
        unreadCount
      };
    } catch (error: any) {
      logger.error('Error getting user notifications:', error);
      throw error;
    }
  }


  static async getNotificationHistory(userId: string, searchTerm?: string): Promise<{
    notifications: Array<{
      id: string;
      type: string;
      title: string;
      message: string;
      amount?: number;
      currency?: string;
      date: string;
      time: string;
      read: boolean;
      category: string;
      icon: string;
      color: string;
    }>;
    summary: {
      total: number;
      unread: number;
      categories: Record<string, number>;
    };
  }> {
    try {
      let query: any = { user_id: userId };
      
      if (searchTerm) {
        query.$or = [
          { title: { $regex: searchTerm, $options: 'i' } },
          { message: { $regex: searchTerm, $options: 'i' } }
        ];
      }

      const notifications = await Notification.find(query)
        .sort({ created_at: -1 })
        .limit(100);

      const total = await Notification.countDocuments({ user_id: userId });
      const unreadCount = await (Notification as any).getUnreadCount(userId);

      // Get category counts
      const categoryStats = await Notification.aggregate([
        { $match: { user_id: new mongoose.Types.ObjectId(userId) } },
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]);

      const categories = categoryStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {} as Record<string, number>);

      const formattedNotifications = notifications.map(notification => ({
        id: notification._id.toString(),
        type: notification.category,
        title: notification.title,
        message: notification.message,
        amount: notification.data?.amount,
        currency: notification.data?.currency,
        date: notification.created_at.toLocaleDateString(),
        time: notification.created_at.toLocaleTimeString(),
        read: notification.read,
        category: notification.category,
        icon: this.getCategoryIcon(notification.category),
        color: this.getCategoryColor(notification.category)
      }));

      return {
        notifications: formattedNotifications,
        summary: {
          total,
          unread: unreadCount,
          categories
        }
      };
    } catch (error: any) {
      logger.error('Error getting notification history:', error);
      throw error;
    }
  }


  static async markAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      const notification = await Notification.findOne({
        _id: notificationId,
        user_id: userId
      });

      if (!notification) return false;

      await (notification as any).markAsRead();
      return true;
    } catch (error: any) {
      logger.error('Error marking notification as read:', error);
      return false;
    }
  }

  static async markAllAsRead(userId: string): Promise<boolean> {
    try {
      await (Notification as any).markAllAsRead(userId);
      return true;
    } catch (error: any) {
      logger.error('Error marking all notifications as read:', error);
      return false;
    }
  }

  /**
   * Format email content
   */
  private static formatEmailContent(notification: INotification): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">${notification.title}</h2>
        <p style="color: #666; line-height: 1.6;">${notification.message}</p>
        <hr style="margin: 20px 0;">
        <p style="color: #999; font-size: 12px;">
          This is an automated message from AeTrust. Please do not reply to this email.
        </p>
      </div>
    `;
  }

  /**
   * Get category icon
   */
  private static getCategoryIcon(category: string): string {
    const icons: Record<string, string> = {
      transaction: '💳',
      security: '🔒',
      account: '👤',
      loan: '💰',
      promotion: '🎁',
      system: '⚙️'
    };
    return icons[category] || '📱';
  }

  /**
   * Get category color
   */
  private static getCategoryColor(category: string): string {
    const colors: Record<string, string> = {
      transaction: '#4CAF50',
      security: '#F44336',
      account: '#2196F3',
      loan: '#FF9800',
      promotion: '#9C27B0',
      system: '#607D8B'
    };
    return colors[category] || '#757575';
  }

  /**
   * Delete notification
   */
  static async deleteNotification(notificationId: string, userId: string): Promise<boolean> {
    try {
      const result = await Notification.deleteOne({
        _id: notificationId,
        user_id: userId
      });

      return result.deletedCount > 0;
    } catch (error: any) {
      logger.error('Error deleting notification:', error);
      return false;
    }
  }


  static async getNotificationSettings(userId: string): Promise<any> {
    try {
      const user = await User.findById(userId).select('preferences.notifications');

      return {
        email: user?.preferences?.notifications?.email ?? true,
        sms: user?.preferences?.notifications?.sms ?? true,
        push: user?.preferences?.notifications?.push ?? true,
        categories: {
          transaction: true,
          security: true,
          account: true,
          loan: true,
          promotion: true,
          system: true
        }
      };
    } catch (error: any) {
      logger.error('Error getting notification settings:', error);
      throw error;
    }
  }


  static async updateNotificationSettings(userId: string, settings: any): Promise<any> {
    try {
      const updateData: any = {};

      if (typeof settings.email === 'boolean') {
        updateData['preferences.notifications.email'] = settings.email;
      }
      if (typeof settings.sms === 'boolean') {
        updateData['preferences.notifications.sms'] = settings.sms;
      }
      if (typeof settings.push === 'boolean') {
        updateData['preferences.notifications.push'] = settings.push;
      }

      await User.findByIdAndUpdate(userId, updateData);

      return await this.getNotificationSettings(userId);
    } catch (error: any) {
      logger.error('Error updating notification settings:', error);
      throw error;
    }
  }

 
  static async sendSecurityAlert(
    userId: string,
    alertType: string,
    details: string
  ): Promise<void> {
    try {
      await this.createNotification({
        userId,
        type: NotificationType.IN_APP,
        title: `Security Alert: ${alertType}`,
        message: details,
        category: 'security',
        priority: 'high',
        data: {
          alertType,
          details,
          timestamp: new Date()
        }
      });

      const user = await User.findById(userId);
      if (user) {
        const { CommunicationService } = await import('./communication.service');
        await CommunicationService.sendSecurityAlert(
          user.phone,
          user.email,
          alertType,
          details,
          user.first_name
        );
      }
    } catch (error: any) {
      logger.error('Error sending security alert:', error);
    }
  }

 
  static async sendLoanNotification(
    userId: string,
    loanType: string,
    message: string,
    data?: any
  ): Promise<void> {
    try {
      await this.createNotification({
        userId,
        type: NotificationType.IN_APP,
        title: `Loan ${loanType}`,
        message,
        category: 'loan',
        priority: 'medium',
        data: data || {}
      });
    } catch (error: any) {
      logger.error('Error sending loan notification:', error);
    }
  }


  static async processScheduledNotifications(): Promise<void> {
    try {
      const scheduledNotifications = await Notification.find({
        status: NotificationStatus.PENDING,
        scheduled_for: { $lte: new Date() }
      });

      for (const notification of scheduledNotifications) {
        await this.sendNotification(notification);
      }

      logger.info(`Processed ${scheduledNotifications.length} scheduled notifications`);
    } catch (error: any) {
      logger.error('Error processing scheduled notifications:', error);
    }
  }


  static async cleanupExpiredNotifications(): Promise<void> {
    try {
      const result = await Notification.deleteMany({
        expires_at: { $lt: new Date() }
      });

      logger.info(`Cleaned up ${result.deletedCount} expired notifications`);
    } catch (error: any) {
      logger.error('Error cleaning up expired notifications:', error);
    }
  }
}
