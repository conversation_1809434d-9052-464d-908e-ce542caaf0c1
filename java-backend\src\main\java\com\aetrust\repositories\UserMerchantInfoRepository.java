package com.aetrust.repositories;

import com.aetrust.models.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserMerchantInfoRepository extends JpaRepository<User.UserMerchantInfo, Long> {

    Optional<User.UserMerchantInfo> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<User.UserMerchantInfo> findByUserUuidAndDeletedAtIsNull(UUID userUuid);
    Optional<User.UserMerchantInfo> findByApiKeyAndDeletedAtIsNull(String apiKey);

    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);
    boolean existsByApiKeyAndDeletedAtIsNull(String apiKey);

    List<User.UserMerchantInfo> findByVerificationStatusAndDeletedAtIsNull(String status);
    List<User.UserMerchantInfo> findByBusinessTypeAndDeletedAtIsNull(String businessType);
    List<User.UserMerchantInfo> findByMerchantCategoryAndDeletedAtIsNull(String category);

    @Query("SELECT m FROM User$UserMerchantInfo m WHERE m.verificationStatus = 'VERIFIED' AND m.apiKey IS NOT NULL AND m.deletedAt IS NULL")
    List<User.UserMerchantInfo> findActiveApiUsers();

    @Query("SELECT COUNT(m) FROM User$UserMerchantInfo m WHERE m.verificationStatus = :status AND m.deletedAt IS NULL")
    long countByVerificationStatus(@Param("status") String status);

    @Query("SELECT m.merchantCategory, COUNT(m) FROM User$UserMerchantInfo m WHERE m.deletedAt IS NULL GROUP BY m.merchantCategory")
    List<Object[]> getMerchantCategoryDistribution();

    @Query("SELECT COUNT(m) FROM User$UserMerchantInfo m WHERE m.apiKey IS NOT NULL AND m.deletedAt IS NULL")
    long countMerchantsWithApiAccess();

    @Modifying
    @Query("UPDATE User$UserMerchantInfo m SET m.deletedAt = :deletedAt WHERE m.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserMerchantInfo m SET m.deletedAt = :deletedAt WHERE m.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserMerchantInfo m SET m.apiKey = null WHERE m.userId = :userId")
    void revokeApiKey(@Param("userId") Long userId);
}
