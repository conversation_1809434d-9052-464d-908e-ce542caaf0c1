# AETrust Backend API - Postman Examples

## 1. ID Verification (Simplified)

**Endpoint:** `POST http://localhost:3000/api/v1/registration/id-verification`

⚠️ **Important:** Make sure you're using the correct endpoint. The application runs with context path `/api/v1`, so the full URL should be:
- ✅ Correct: `http://localhost:3000/api/v1/registration/id-verification`
- ❌ Wrong: `http://localhost:3000/api/v1/auth/registration/id-verification`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "platform": "web",
  "email": "<EMAIL>",
  "phone": "+250788123456",
  "idNumber": "25456644444",
  "idType": "national_id",
  "idDocumentFront": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
  "idDocumentBack": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
}
```

**Notes:**
- `selfiePhoto` field has been **removed** - no longer required
- `idDocumentBack` is **optional** - only include if you have it
- For passport, you typically only need `idDocumentFront`
- For national_id and drivers_license, both front and back are recommended but back is optional

## 2. Business Verification (Simplified for Agents)

**Endpoint:** `POST http://localhost:3000/api/v1/registration/business-verification`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "phone": "+250788123456",
  "email": "<EMAIL>",
  "businessName": "Josh Digital Solutions",
  "businessRegistrationNumber": "BN123456789",
  "businessAddress": "KG 15 Ave, Kigali, Rwanda, 00100",
  "businessDocument": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
  "bankAccountNumber": "****************",
  "bankName": "Bank of Kigali",
  "accountHolderName": "Josh Digital Solutions",
  "platform": "web"
}
```

**Removed Fields:**
- `businessType` - no longer required
- `taxId` - removed
- `taxCertificate` - removed
- Complex `businessAddress` object - now simple string

**New Required Fields:**
- `bankAccountNumber` - for settlement
- `bankName` - bank name for settlement
- `accountHolderName` - account holder name

## 3. File Upload Examples

### Single Base64 Upload
**Endpoint:** `POST http://localhost:3000/api/v1/upload/base64`

```json
{
  "fileData": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
  "uploadType": "ID_DOCUMENT_FRONT",
  "userId": "550e8400-e29b-41d4-a716-************"
}
```

### Multiple Base64 Upload
**Endpoint:** `POST http://localhost:3000/api/v1/upload/base64/multiple`

```json
{
  "files": {
    "idDocumentFront": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
    "idDocumentBack": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."
  },
  "uploadType": "ID_DOCUMENT_FRONT",
  "userId": "550e8400-e29b-41d4-a716-************"
}
```

## Available Upload Types:
- `PROFILE_PICTURE`
- `ID_DOCUMENT_FRONT`
- `ID_DOCUMENT_BACK`
- `BUSINESS_DOCUMENT`
- `DOCUMENTS`
- `CHAT_MEDIA`

## Expected Success Response:
```json
{
  "success": true,
  "message": "ID verification submitted successfully",
  "data": {
    "userId": "123",
    "nextStep": "business_verification"
  },
  "timestamp": *************
}
```

## Expected Error Response:
```json
{
  "success": false,
  "message": "validation failed",
  "error_code": "VALIDATION_ERROR",
  "errors": [
    {
      "field": "idDocumentFront",
      "message": "ID document front is required"
    }
  ],
  "timestamp": *************
}
```

## Key Changes Made:

1. **Removed selfie requirement** from ID verification
2. **Made idDocumentBack optional** - only upload if available
3. **Simplified business verification** - removed tax fields, complex address
4. **Added bank account details** for agent settlement
5. **Removed personal info endpoint** - no longer needed
6. **Streamlined validation** - fewer required fields

## File Upload Features:

- **Cloudinary Integration**: Automatically uploads to Cloudinary if configured
- **Local Storage Fallback**: Uses local storage if Cloudinary not available
- **Security Validation**: File type, size, and content validation
- **Base64 Support**: Direct base64 upload without multipart forms
- **Multiple Upload**: Batch upload multiple files at once
