{"info": {"_postman_id": "aetrust-java-backend-api", "name": "AETrust Java Backend API", "description": "Complete API collection for AETrust fintech platform backend with authentication, registration, user management, and system endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "aetrust-team"}, "item": [{"name": "Health & System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Health Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health/status", "host": ["{{baseUrl}}"], "path": ["health", "status"]}}}, {"name": "System Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/system/health", "host": ["{{baseUrl}}"], "path": ["system", "health"]}}}, {"name": "System Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/system/config", "host": ["{{baseUrl}}"], "path": ["system", "config"]}}}, {"name": "API Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/system/info", "host": ["{{baseUrl}}"], "path": ["system", "info"]}}}, {"name": "Validation Requirements", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/system/validation-requirements", "host": ["{{baseUrl}}"], "path": ["system", "validation-requirements"]}}}]}, {"name": "Database", "item": [{"name": "Database Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/health", "host": ["{{baseUrl}}"], "path": ["database", "health"]}}}, {"name": "Database Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/config", "host": ["{{baseUrl}}"], "path": ["database", "config"]}}}, {"name": "Database Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/info", "host": ["{{baseUrl}}"], "path": ["database", "info"]}}}, {"name": "Test Connection", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/test-connection", "host": ["{{baseUrl}}"], "path": ["database", "test-connection"]}}}, {"name": "List Tables", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/tables", "host": ["{{baseUrl}}"], "path": ["database", "tables"]}}}, {"name": "Database Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/stats", "host": ["{{baseUrl}}"], "path": ["database", "stats"]}}}, {"name": "Database Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/database/db-test", "host": ["{{baseUrl}}"], "path": ["database", "db-test"]}}}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.access_token) {", "        pm.environment.set('accessToken', response.data.access_token);", "        pm.environment.set('refreshToken', response.data.refresh_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+250788123456\",\n    \"password\": \"SecurePass123!\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+250788123456\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+250788123456\",\n    \"reset_code\": \"123456\",\n    \"new_password\": \"NewSecurePass123!\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password"]}}}, {"name": "Auth Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/health", "host": ["{{baseUrl}}"], "path": ["auth", "health"]}}}]}, {"name": "Registration", "item": [{"name": "Initiate Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+250788123456\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Doe\",\n    \"userType\": \"individual\",\n    \"platform\": \"web\",\n    \"dateOfBirth\": \"1990-01-01\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/initiate", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "initiate"]}}}, {"name": "Verify Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+250788123456\",\n    \"code\": \"123456\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/verify-phone", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "verify-phone"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"code\": \"123456\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/verify-email", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "verify-email"]}}}, {"name": "Complete Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+250788123456\",\n    \"password\": \"SecurePass123!\",\n    \"transactionPin\": \"1234\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Doe\",\n    \"userType\": \"individual\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/complete", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "complete"]}}}, {"name": "Send Email Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/registration/send-email-verification?email=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "send-email-verification"], "query": [{"key": "email", "value": "<EMAIL>"}]}}}, {"name": "Personal Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"dateOfBirth\": \"1990-01-01\",\n    \"address\": \"123 Main St, Kigali\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/personal-info", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "personal-info"]}}}, {"name": "KYC Documents", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"documentType\": \"national_id\",\n    \"documentNumber\": \"1234567890123456\",\n    \"frontImageUrl\": \"https://example.com/front.jpg\",\n    \"backImageUrl\": \"https://example.com/back.jpg\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/kyc-documents", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "kyc-documents"]}}}, {"name": "Set Transaction PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionPin\": \"1234\",\n    \"confirmPin\": \"1234\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/transaction-pin", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "transaction-pin"]}}}, {"name": "Biometric Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"biometricType\": \"fingerprint\",\n    \"biometricData\": \"base64_encoded_data\",\n    \"platform\": \"mobile\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/biometric-setup", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "biometric-setup"]}}}, {"name": "Agent Business Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"businessName\": \"John's Money Transfer\",\n    \"businessType\": \"money_transfer\",\n    \"businessAddress\": \"123 Business St, Kigali\",\n    \"businessLicense\": \"BL123456789\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/agent-business-info", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "agent-business-info"]}}}, {"name": "Registration Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/registration/progress", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "progress"]}}}, {"name": "Resend Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"type\": \"sms\",\n    \"phone\": \"+250788123456\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/resend-verification", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "resend-verification"]}}}, {"name": "Validation Requirements", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/registration/validation-requirements", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "validation-requirements"]}}}, {"name": "Test Validation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+250788123456\",\n    \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/registration/test-validation", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "test-validation"]}}}, {"name": "Registration Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/registration/health", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "health"]}}}, {"name": "Registration Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/registration/config", "host": ["{{baseUrl}}"], "path": ["auth", "registration", "config"]}}}]}, {"name": "User Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"address\": \"123 Updated St, Kigali\"\n}"}, "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}}}, {"name": "Update Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"currentPassword\": \"OldPassword123!\",\n    \"newPassword\": \"NewPassword123!\",\n    \"confirmPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/users/password", "host": ["{{baseUrl}}"], "path": ["users", "password"]}}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"imageData\": \"base64_encoded_image_data\",\n    \"fileName\": \"profile.jpg\",\n    \"fileType\": \"image/jpeg\"\n}"}, "url": {"raw": "{{baseUrl}}/users/profile-picture", "host": ["{{baseUrl}}"], "path": ["users", "profile-picture"]}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"password\": \"UserPassword123!\",\n    \"reason\": \"No longer needed\"\n}"}, "url": {"raw": "{{baseUrl}}/users/account", "host": ["{{baseUrl}}"], "path": ["users", "account"]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/stats", "host": ["{{baseUrl}}"], "path": ["users", "stats"]}}}, {"name": "Verify Email <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/verify/{{emailToken}}", "host": ["{{baseUrl}}"], "path": ["users", "verify", "{{emailToken}}"]}}}, {"name": "Get Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/dashboard", "host": ["{{baseUrl}}"], "path": ["users", "dashboard"]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/search?query=john&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["users", "search"], "query": [{"key": "query", "value": "john"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "User Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/health", "host": ["{{baseUrl}}"], "path": ["users", "health"]}}}, {"name": "User Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/config", "host": ["{{baseUrl}}"], "path": ["users", "config"]}}}]}, {"name": "Cache Management", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cache/info", "host": ["{{baseUrl}}"], "path": ["cache", "info"]}}}, {"name": "Cache Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cache/health", "host": ["{{baseUrl}}"], "path": ["cache", "health"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cache/config", "host": ["{{baseUrl}}"], "path": ["cache", "config"]}}}, {"name": "Test Cache", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"test_key\",\n    \"value\": \"test_value\"\n}"}, "url": {"raw": "{{baseUrl}}/cache/test", "host": ["{{baseUrl}}"], "path": ["cache", "test"]}}}, {"name": "<PERSON>ache", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/cache/clear", "host": ["{{baseUrl}}"], "path": ["cache", "clear"]}}}, {"name": "Get Cache Value", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cache/get/{{cacheKey}}", "host": ["{{baseUrl}}"], "path": ["cache", "get", "{{cacheKey}}"]}}}, {"name": "Set Cache Value", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"my_key\",\n    \"value\": \"my_value\",\n    \"ttl\": 3600\n}"}, "url": {"raw": "{{baseUrl}}/cache/set", "host": ["{{baseUrl}}"], "path": ["cache", "set"]}}}, {"name": "Delete Cache Key", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/cache/delete/{{cacheKey}}", "host": ["{{baseUrl}}"], "path": ["cache", "delete", "{{cacheKey}}"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "emailToken", "value": "", "type": "string"}, {"key": "cache<PERSON>ey", "value": "test_key", "type": "string"}]}