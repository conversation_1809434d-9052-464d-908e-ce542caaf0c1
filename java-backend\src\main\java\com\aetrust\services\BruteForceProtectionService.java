package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BruteForceProtectionService {

    @Autowired
    private CacheService cacheService;

    @Value("${aetrust.security.max-login-attempts:5}")
    private int maxLoginAttempts;

    @Value("${aetrust.security.lockout-duration:900000}")
    private long lockoutDurationMs;

    @Value("${aetrust.feature-flags.brute-force-protection.enabled:true}")
    private boolean bruteForceProtectionEnabled;

    public void recordFailedAttempt(String identifier, String type) {
        if (!bruteForceProtectionEnabled) {
            return;
        }

        String key = "bruteforce:" + type + ":" + identifier;
        String countKey = key + ":count";
        String lockKey = key + ":locked";

        try {
            // check if already locked
            if (isLocked(identifier, type)) {
                return;
            }

            // increment attempt count
            String currentCount = cacheService.get(countKey);
            int attempts = currentCount != null ? Integer.parseInt(currentCount) : 0;
            attempts++;

            // store attempt count with expiration
            cacheService.set(countKey, String.valueOf(attempts), lockoutDurationMs, TimeUnit.MILLISECONDS);

            log.warn("Failed {} attempt #{} for identifier: {}", type, attempts, maskIdentifier(identifier));

            // lock if max attempts reached
            if (attempts >= maxLoginAttempts) {
                lockAccount(identifier, type);
                log.warn("Account locked due to {} failed {} attempts for identifier: {}", 
                        attempts, type, maskIdentifier(identifier));
            }

        } catch (Exception e) {
            log.error("Error recording failed attempt for {}: {}", identifier, e.getMessage());
        }
    }

    public void recordSuccessfulAttempt(String identifier, String type) {
        if (!bruteForceProtectionEnabled) {
            return;
        }

        String key = "bruteforce:" + type + ":" + identifier;
        String countKey = key + ":count";
        String lockKey = key + ":locked";

        try {
            // clear attempt count and lock on successful login
            cacheService.delete(countKey);
            cacheService.delete(lockKey);

            log.info("Successful {} for identifier: {} - cleared failed attempts", type, maskIdentifier(identifier));

        } catch (Exception e) {
            log.error("Error clearing failed attempts for {}: {}", identifier, e.getMessage());
        }
    }

    public boolean isLocked(String identifier, String type) {
        if (!bruteForceProtectionEnabled) {
            return false;
        }

        String key = "bruteforce:" + type + ":" + identifier;
        String lockKey = key + ":locked";

        try {
            String lockTime = cacheService.get(lockKey);
            if (lockTime != null) {
                LocalDateTime lockedAt = LocalDateTime.parse(lockTime);
                LocalDateTime unlockTime = lockedAt.plus(lockoutDurationMs, ChronoUnit.MILLIS);
                
                if (LocalDateTime.now().isBefore(unlockTime)) {
                    return true;
                } else {
                    // lock expired, remove it
                    cacheService.delete(lockKey);
                    cacheService.delete(key + ":count");
                }
            }
        } catch (Exception e) {
            log.error("Error checking lock status for {}: {}", identifier, e.getMessage());
        }

        return false;
    }

    public int getRemainingAttempts(String identifier, String type) {
        if (!bruteForceProtectionEnabled) {
            return maxLoginAttempts;
        }

        String key = "bruteforce:" + type + ":" + identifier;
        String countKey = key + ":count";

        try {
            String currentCount = cacheService.get(countKey);
            int attempts = currentCount != null ? Integer.parseInt(currentCount) : 0;
            return Math.max(0, maxLoginAttempts - attempts);
        } catch (Exception e) {
            log.error("Error getting remaining attempts for {}: {}", identifier, e.getMessage());
            return maxLoginAttempts;
        }
    }

    public long getLockoutTimeRemaining(String identifier, String type) {
        if (!bruteForceProtectionEnabled) {
            return 0;
        }

        String key = "bruteforce:" + type + ":" + identifier;
        String lockKey = key + ":locked";

        try {
            String lockTime = cacheService.get(lockKey);
            if (lockTime != null) {
                LocalDateTime lockedAt = LocalDateTime.parse(lockTime);
                LocalDateTime unlockTime = lockedAt.plus(lockoutDurationMs, ChronoUnit.MILLIS);
                LocalDateTime now = LocalDateTime.now();
                
                if (now.isBefore(unlockTime)) {
                    return ChronoUnit.SECONDS.between(now, unlockTime);
                }
            }
        } catch (Exception e) {
            log.error("Error getting lockout time remaining for {}: {}", identifier, e.getMessage());
        }

        return 0;
    }

    private void lockAccount(String identifier, String type) {
        String key = "bruteforce:" + type + ":" + identifier;
        String lockKey = key + ":locked";

        try {
            cacheService.set(lockKey, LocalDateTime.now().toString(), lockoutDurationMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("Error locking account for {}: {}", identifier, e.getMessage());
        }
    }

    private String maskIdentifier(String identifier) {
        if (identifier == null || identifier.length() <= 4) {
            return "****";
        }
        
        if (identifier.contains("@")) {
            // email
            String[] parts = identifier.split("@");
            String username = parts[0];
            String domain = parts[1];
            
            if (username.length() <= 2) {
                return "**@" + domain;
            }
            
            return username.substring(0, 2) + "****@" + domain;
        } else {
            // phone or other identifier
            return identifier.substring(0, 2) + "****" + identifier.substring(identifier.length() - 2);
        }
    }

    public BruteForceStatus getStatus(String identifier, String type) {
        boolean locked = isLocked(identifier, type);
        int remainingAttempts = getRemainingAttempts(identifier, type);
        long lockoutTimeRemaining = getLockoutTimeRemaining(identifier, type);

        return new BruteForceStatus(locked, remainingAttempts, lockoutTimeRemaining);
    }

    public static class BruteForceStatus {
        private final boolean locked;
        private final int remainingAttempts;
        private final long lockoutTimeRemaining;

        public BruteForceStatus(boolean locked, int remainingAttempts, long lockoutTimeRemaining) {
            this.locked = locked;
            this.remainingAttempts = remainingAttempts;
            this.lockoutTimeRemaining = lockoutTimeRemaining;
        }

        public boolean isLocked() {
            return locked;
        }

        public int getRemainingAttempts() {
            return remainingAttempts;
        }

        public long getLockoutTimeRemaining() {
            return lockoutTimeRemaining;
        }
    }
}
