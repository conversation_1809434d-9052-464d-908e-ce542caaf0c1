# Registration Continuation API

This document describes the new endpoints that allow users to continue their registration process if they stop midway or check their registration status.

## Overview

The registration process in AETrust is multi-step and can be interrupted. Users can now:
1. Check if they have an existing registration in progress
2. Continue their registration from where they left off
3. Get detailed information about what step they need to complete next

## New Endpoints

### 1. Check Registration Status

**Endpoint:** `POST /api/v1/auth/registration/check-status`

**Purpose:** Check if a user has an existing registration session and get its current status.

**Request Body:**
```json
{
  "identifier": "+250788123456",  // phone or email
  "platform": "web"
}
```

**Response (Registration Found):**
```json
{
  "success": true,
  "message": "registration status retrieved",
  "data": {
    "registrationFound": true,
    "currentStep": "phone_verification",
    "phoneVerified": false,
    "emailVerified": false,
    "completed": false,
    "canContinue": true,
    "nextAction": "verify_phone",
    "message": "registration in progress",
    "requiresLogin": false,
    "requiresRestart": false
  }
}
```

**Response (Registration Not Found):**
```json
{
  "success": false,
  "message": "no registration found",
  "errorCode": "REGISTRATION_NOT_FOUND"
}
```

**Response (Registration Expired):**
```json
{
  "success": true,
  "message": "registration status retrieved",
  "data": {
    "registrationFound": true,
    "currentStep": "phone_verification",
    "phoneVerified": false,
    "emailVerified": false,
    "completed": false,
    "canContinue": false,
    "nextAction": "restart_registration",
    "message": "registration session expired, please start over",
    "requiresLogin": false,
    "requiresRestart": true
  }
}
```

### 2. Continue Registration

**Endpoint:** `POST /api/v1/auth/registration/continue`

**Purpose:** Get detailed information about how to continue an existing registration.

**Request Body:**
```json
{
  "identifier": "+250788123456",  // phone or email
  "platform": "web"
}
```

**Response:**
```json
{
  "success": true,
  "message": "registration continuation details",
  "data": {
    "registrationId": "reg_abc123def456",
    "currentStep": "phone_verification",
    "nextStep": "email_verification",
    "phoneVerified": false,
    "emailVerified": false,
    "stepInstructions": "verify your phone number with the SMS code sent to you",
    "requiredFields": {
      "required": ["phone", "code"],
      "endpoint": "/auth/registration/verify-phone"
    }
  }
}
```

## Registration Steps and Next Actions

| Current Step | Next Action | Description |
|--------------|-------------|-------------|
| `phone_verification` | `verify_phone` | User needs to verify their phone number |
| `email_verification` | `verify_email` | User needs to verify their email address |
| `personal_info` | `complete_personal_info` | User needs to complete personal information |
| `transaction_pin` | `set_transaction_pin` | User needs to set up transaction PIN |
| `identity_verification` | `submit_identity_documents` | User needs to upload identity documents |
| `biometric_enrollment` | `setup_biometric` | User needs to set up biometric authentication |
| `business_verification` | `submit_business_info` | Agent users need to submit business information |

## Error Handling

### Common Error Responses

**Rate Limit Exceeded:**
```json
{
  "success": false,
  "message": "too many requests",
  "errorCode": "RATE_LIMIT_EXCEEDED"
}
```

**Session Expired:**
```json
{
  "success": false,
  "message": "registration session expired",
  "errorCode": "SESSION_EXPIRED"
}
```

**Already Completed:**
```json
{
  "success": false,
  "message": "registration already completed",
  "errorCode": "ALREADY_COMPLETED"
}
```

## Usage Flow

### Typical User Journey

1. **User returns to app/website**
2. **Check if registration exists:**
   ```
   POST /auth/registration/check-status
   ```
3. **If registration found and can continue:**
   ```
   POST /auth/registration/continue
   ```
4. **Follow the instructions to complete the current step**
5. **Repeat until registration is completed**

### Frontend Implementation Example

```javascript
async function checkAndContinueRegistration(identifier) {
  try {
    // Check if registration exists
    const statusResponse = await fetch('/api/v1/auth/registration/check-status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: identifier,
        platform: 'web'
      })
    });
    
    const statusData = await statusResponse.json();
    
    if (!statusData.success) {
      // No registration found, start new registration
      return { action: 'start_new_registration' };
    }
    
    if (statusData.data.requiresRestart) {
      // Registration expired, start over
      return { action: 'restart_registration' };
    }
    
    if (statusData.data.requiresLogin) {
      // Registration completed, redirect to login
      return { action: 'redirect_to_login' };
    }
    
    if (statusData.data.canContinue) {
      // Get continuation details
      const continueResponse = await fetch('/api/v1/auth/registration/continue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          identifier: identifier,
          platform: 'web'
        })
      });
      
      const continueData = await continueResponse.json();
      
      return {
        action: 'continue_registration',
        step: continueData.data.currentStep,
        instructions: continueData.data.stepInstructions,
        requiredFields: continueData.data.requiredFields,
        registrationId: continueData.data.registrationId
      };
    }
    
  } catch (error) {
    console.error('Error checking registration status:', error);
    return { action: 'error', error: error.message };
  }
}
```

## Security Considerations

1. **Rate Limiting:** Both endpoints are rate-limited to prevent abuse
2. **Data Masking:** Sensitive information is not exposed in responses
3. **Session Validation:** Registration sessions have expiration times
4. **Input Validation:** All inputs are validated for security
5. **IP Tracking:** Requests are logged with IP addresses for security monitoring

## Testing

Use the provided endpoints to test the registration continuation flow:

1. Start a registration but don't complete it
2. Use the check-status endpoint to verify it's found
3. Use the continue endpoint to get next steps
4. Complete the registration step by step
