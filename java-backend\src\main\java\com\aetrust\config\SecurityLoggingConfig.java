package com.aetrust.config;

import com.aetrust.services.ValidationService;
import com.aetrust.utils.ControllerUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Slf4j
@Configuration
public class SecurityLoggingConfig {
    
    @Autowired
    private ValidationService validationService;

    @Autowired
    private ControllerUtils controllerUtils;
    
    // Sensitive endpoints that require detailed logging
    private static final List<String> SENSITIVE_ENDPOINTS = Arrays.asList(
        "/auth/registration",
        "/auth/login",
        "/auth/refresh",
        "/users",
        "/admin",
        "/agent"
    );
    
    // Endpoints to exclude from detailed logging (health checks, etc.)
    private static final List<String> EXCLUDED_ENDPOINTS = Arrays.asList(
        "/actuator",
        "/system/health",
        "/system/info"
    );
    
    @Bean
    public SecurityLoggingFilter securityLoggingFilter() {
        return new SecurityLoggingFilter();
    }
    
    public class SecurityLoggingFilter extends OncePerRequestFilter {
        
        @Override
        protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                FilterChain filterChain) throws ServletException, IOException {
            
            String requestId = UUID.randomUUID().toString().substring(0, 8);
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            String clientIp = controllerUtils.getClientIp(request);
            String userAgent = request.getHeader("User-Agent");
            
            // Skip logging for excluded endpoints
            if (shouldExcludeFromLogging(requestURI)) {
                filterChain.doFilter(request, response);
                return;
            }
            
            // Wrap request and response for content caching
            ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
            ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);
            
            long startTime = System.currentTimeMillis();
            
            try {
                // Log incoming request
                logIncomingRequest(requestId, wrappedRequest, clientIp, userAgent);
                
                // Process the request
                filterChain.doFilter(wrappedRequest, wrappedResponse);
                
                // Log outgoing response
                logOutgoingResponse(requestId, wrappedRequest, wrappedResponse, 
                    System.currentTimeMillis() - startTime);
                
            } catch (Exception e) {
                // Log error
                logRequestError(requestId, wrappedRequest, e, 
                    System.currentTimeMillis() - startTime);
                throw e;
            } finally {
                // Copy response content back
                wrappedResponse.copyBodyToResponse();
            }
        }
        
        private void logIncomingRequest(String requestId, HttpServletRequest request, 
                String clientIp, String userAgent) {
            
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            
            if (isSensitiveEndpoint(requestURI)) {
                // Detailed logging for sensitive endpoints
                log.info("SECURITY_REQUEST [{}] {} {} from IP: {} | UserAgent: {} | Timestamp: {}", 
                    requestId, method, requestURI, clientIp, 
                    sanitizeUserAgent(userAgent), LocalDateTime.now());
                
                // Log headers (excluding sensitive ones)
                logSafeHeaders(requestId, request);
                
                // Log request parameters (sanitized)
                logSafeParameters(requestId, request);
                
            } else {
                // Basic logging for other endpoints
                log.info("REQUEST [{}] {} {} from {}", requestId, method, requestURI, clientIp);
            }
        }
        
        private void logOutgoingResponse(String requestId, HttpServletRequest request, 
                HttpServletResponse response, long duration) {
            
            String requestURI = request.getRequestURI();
            int status = response.getStatus();
            
            if (isSensitiveEndpoint(requestURI)) {
                // Detailed logging for sensitive endpoints
                log.info("SECURITY_RESPONSE [{}] {} {} | Status: {} | Duration: {}ms | Timestamp: {}", 
                    requestId, request.getMethod(), requestURI, status, duration, LocalDateTime.now());
                
                // Log security-relevant response headers
                logSecurityHeaders(requestId, response);
                
                // Alert on suspicious response patterns
                if (status == 401 || status == 403) {
                    log.warn("SECURITY_ALERT [{}] Authentication/Authorization failure | Status: {} | URI: {} | IP: {}", 
                        requestId, status, requestURI, controllerUtils.getClientIp(request));
                }
                
                if (status >= 500) {
                    log.error("SECURITY_ERROR [{}] Server error | Status: {} | URI: {} | IP: {}", 
                        requestId, status, requestURI, controllerUtils.getClientIp(request));
                }
                
            } else {
                // Basic logging for other endpoints
                log.info("RESPONSE [{}] Status: {} | Duration: {}ms", requestId, status, duration);
            }
        }
        
        private void logRequestError(String requestId, HttpServletRequest request, 
                Exception error, long duration) {
            
            String requestURI = request.getRequestURI();
            String clientIp = controllerUtils.getClientIp(request);
            
            log.error("REQUEST_ERROR [{}] {} {} from {} | Duration: {}ms | Error: {}", 
                requestId, request.getMethod(), requestURI, clientIp, duration, error.getMessage());
            
            // Additional security logging for sensitive endpoints
            if (isSensitiveEndpoint(requestURI)) {
                log.error("SECURITY_EXCEPTION [{}] Exception in sensitive endpoint | URI: {} | IP: {} | Error: {}", 
                    requestId, requestURI, clientIp, error.getClass().getSimpleName());
            }
        }
        
        private void logSafeHeaders(String requestId, HttpServletRequest request) {
            // Log only safe headers, exclude sensitive ones
            List<String> safeHeaders = Arrays.asList(
                "Content-Type", "Accept", "Accept-Language", "Accept-Encoding", 
                "Cache-Control", "Connection", "Host", "Origin", "Referer"
            );
            
            StringBuilder headerLog = new StringBuilder();
            for (String headerName : safeHeaders) {
                String headerValue = request.getHeader(headerName);
                if (headerValue != null) {
                    headerLog.append(headerName).append(": ")
                        .append(validationService.sanitizeInput(headerValue))
                        .append(" | ");
                }
            }
            
            if (headerLog.length() > 0) {
                log.debug("HEADERS [{}] {}", requestId, headerLog.toString());
            }
        }
        
        private void logSafeParameters(String requestId, HttpServletRequest request) {
            // Log request parameters (sanitized, excluding sensitive ones)
            List<String> sensitiveParams = Arrays.asList(
                "password", "pin", "code", "token", "secret", "key"
            );
            
            StringBuilder paramLog = new StringBuilder();
            request.getParameterMap().forEach((name, values) -> {
                if (!sensitiveParams.contains(name.toLowerCase())) {
                    paramLog.append(name).append(": ")
                        .append(validationService.sanitizeInput(String.join(",", values)))
                        .append(" | ");
                }
            });
            
            if (paramLog.length() > 0) {
                log.debug("PARAMS [{}] {}", requestId, paramLog.toString());
            }
        }
        
        private void logSecurityHeaders(String requestId, HttpServletResponse response) {
            // Log security-relevant response headers
            List<String> securityHeaders = Arrays.asList(
                "X-Frame-Options", "X-Content-Type-Options", "X-XSS-Protection",
                "Strict-Transport-Security", "Content-Security-Policy", "Referrer-Policy"
            );
            
            StringBuilder headerLog = new StringBuilder();
            for (String headerName : securityHeaders) {
                String headerValue = response.getHeader(headerName);
                if (headerValue != null) {
                    headerLog.append(headerName).append(": ").append(headerValue).append(" | ");
                }
            }
            
            if (headerLog.length() > 0) {
                log.debug("SECURITY_HEADERS [{}] {}", requestId, headerLog.toString());
            }
        }
        
        private boolean isSensitiveEndpoint(String requestURI) {
            return SENSITIVE_ENDPOINTS.stream().anyMatch(requestURI::startsWith);
        }
        
        private boolean shouldExcludeFromLogging(String requestURI) {
            return EXCLUDED_ENDPOINTS.stream().anyMatch(requestURI::startsWith);
        }
        
        private String sanitizeUserAgent(String userAgent) {
            if (userAgent == null) return "unknown";
            return validationService.sanitizeInput(
                userAgent.length() > 100 ? userAgent.substring(0, 100) + "..." : userAgent
            );
        }
        

    }
}
