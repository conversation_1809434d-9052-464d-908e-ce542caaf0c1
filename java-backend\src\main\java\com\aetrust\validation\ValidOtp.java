package com.aetrust.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = OtpValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidOtp {
    String message() default "OTP code must be exactly 6 digits";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
