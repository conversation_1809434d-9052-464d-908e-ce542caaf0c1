package com.aetrust.models;

import com.aetrust.types.Types.AgentStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agents")
@EntityListeners(AuditingEntityListener.class)
public class Agent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String userId;

    @Column(unique = true, nullable = false)
    private String agentCode;
    
    private String businessName;
    private String businessType;
    private String businessRegistrationNumber;
    private String taxId;

    @Embedded
    private Location location;

    @Embedded
    private ContactInfo contactInfo;

    @Column(columnDefinition = "TEXT")
    private String operatingHours; // JSON string

    @Column(columnDefinition = "TEXT")
    private String services; // JSON string

    @Column(columnDefinition = "TEXT")
    private String compliance; // JSON string

    @Column(columnDefinition = "TEXT")
    private String performance; // JSON string
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AgentStatus status = AgentStatus.PENDING;
    private Double commissionRate = 0.0;
    private Double totalCommissions = 0.0;
    private Double cashBalance = 0.0;
    private Double floatBalance = 0.0;
    
    private String approvedBy;
    private LocalDateTime approvedAt;
    private String rejectionReason;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    // nested classes for embedded documents
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class Location {
        private String address;
        private String city;
        private String state;
        private String country;
        private String postalCode;

        @Embedded
        private Coordinates coordinates;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class Coordinates {
        private Double latitude;
        private Double longitude;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Embeddable
    public static class ContactInfo {
        private String phone;
        private String email;
        private String whatsapp;
        private String alternatePhone;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperatingHours {
        private Integer startHour = 8;
        private Integer endHour = 20;
        private List<String> workingDays;
        private boolean isOpen24Hours = false;
        private Map<String, String> specialHours;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Services {
        private boolean cashIn = true;
        private boolean cashOut = true;
        private boolean billPayments = false;
        private boolean mobileTopup = false;
        private boolean moneyTransfer = true;
        private List<String> supportedCurrencies;
        private Double dailyLimit = 50000.0;
        private Double transactionLimit = 5000.0;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Compliance {
        private boolean kycCompleted = false;
        private boolean documentsVerified = false;
        private boolean backgroundCheckPassed = false;
        private LocalDateTime lastAudit;
        private String auditStatus;
        private List<String> requiredDocuments;
        private List<String> submittedDocuments;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Performance {
        private Integer totalTransactions = 0;
        private Double totalVolume = 0.0;
        private Double averageRating = 0.0;
        private Integer totalRatings = 0;
        private Integer successfulTransactions = 0;
        private Integer failedTransactions = 0;
        private Double successRate = 0.0;
        private LocalDateTime lastTransaction;
        private MonthlyStats monthlyStats;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyStats {
        private Integer transactionCount = 0;
        private Double transactionVolume = 0.0;
        private Double commissionsEarned = 0.0;
        private String month;
        private Integer year;
    }
    
    // helper methods
    public boolean isActive() {
        return status == AgentStatus.ACTIVE;
    }
    
    public boolean canPerformTransaction(Double amount) {
        if (!isActive()) return false;
        if (services == null || services.trim().isEmpty()) return false;

        // For now, return true - in production, parse JSON and check transaction limit
        // TODO: Parse services JSON and check transactionLimit
        return true;
    }
    
    public String getDisplayName() {
        return businessName != null ? businessName : "Agent " + agentCode;
    }
}
