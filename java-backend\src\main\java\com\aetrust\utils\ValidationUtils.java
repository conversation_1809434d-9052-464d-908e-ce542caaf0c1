package com.aetrust.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;
import java.util.List;
import java.util.ArrayList;
import java.time.LocalDate;
import java.time.Period;

@Slf4j
@Component
public class ValidationUtils {
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^\\+[1-9]\\d{1,14}$");
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])");
    private static final Pattern OBJECT_ID_PATTERN = Pattern.compile("^[0-9a-fA-F]{24}$");
    private static final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private static final Pattern PIN_PATTERN = Pattern.compile("^\\d{4}$");
    private static final Pattern VERIFICATION_CODE_PATTERN = Pattern.compile("^\\d{6}$");
    
  
    public boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email.trim().toLowerCase()).matches();
    }
    
    
    public boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone.trim()).matches();
    }
    
   
    public boolean isValidPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).find();
    }
    

    public boolean isValidObjectId(String id) {
        if (id == null || id.trim().isEmpty()) {
            return false;
        }
        return OBJECT_ID_PATTERN.matcher(id.trim()).matches();
    }
    
    
    public boolean isValidUUID(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return false;
        }
        return UUID_PATTERN.matcher(uuid.trim()).matches();
    }
    

    public boolean isValidPin(String pin) {
        if (pin == null || pin.trim().isEmpty()) {
            return false;
        }
        return PIN_PATTERN.matcher(pin.trim()).matches();
    }
    
  
    public boolean isValidVerificationCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        return VERIFICATION_CODE_PATTERN.matcher(code.trim()).matches();
    }
    
   
    public boolean isValidAmount(Double amount) {
        if (amount == null || amount <= 0) {
            return false;
        }
        
        // check for max 2 decimal places
        String amountStr = amount.toString();
        int decimalIndex = amountStr.indexOf('.');
        if (decimalIndex != -1) {
            int decimalPlaces = amountStr.length() - decimalIndex - 1;
            return decimalPlaces <= 2;
        }
        
        return true;
    }
    
    
    public boolean isValidAge(LocalDate dateOfBirth) {
        if (dateOfBirth == null) {
            return false;
        }
        
        LocalDate now = LocalDate.now();
        Period age = Period.between(dateOfBirth, now);
        return age.getYears() >= 18;
    }
    
    
    public boolean isValidLength(String value, int minLength, int maxLength) {
        if (value == null) {
            return minLength == 0;
        }
        
        int length = value.trim().length();
        return length >= minLength && length <= maxLength;
    }
    
   
    public boolean isRequired(String value) {
        return value != null && !value.trim().isEmpty();
    }
    
    
    public boolean isValidCurrency(String currency) {
        if (currency == null || currency.trim().isEmpty()) {
            return false;
        }
        
        List<String> validCurrencies = List.of("USD", "EUR", "GBP", "RWF", "KES", "UGX", "TZS");
        return validCurrencies.contains(currency.trim().toUpperCase());
    }
    
    
    public boolean isValidPlatform(String platform) {
        if (platform == null || platform.trim().isEmpty()) {
            return false;
        }
        
        List<String> validPlatforms = List.of("web", "app", "mobile_web");
        return validPlatforms.contains(platform.trim().toLowerCase());
    }
    
   
    public boolean isValidIdType(String idType) {
        if (idType == null || idType.trim().isEmpty()) {
            return false;
        }
        
        List<String> validIdTypes = List.of("national_id", "passport", "drivers_license", "voters_card");
        return validIdTypes.contains(idType.trim().toLowerCase());
    }
    
   
    public boolean isValidBusinessType(String businessType) {
        if (businessType == null || businessType.trim().isEmpty()) {
            return false;
        }
        
        List<String> validTypes = List.of("retail_shop", "mobile_money_agent", "bank_agent", "pharmacy", "supermarket", "other");
        return validTypes.contains(businessType.trim().toLowerCase());
    }
    
    
    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        return input.trim()
                .replaceAll("<script[^>]*>.*?</script>", "")
                .replaceAll("<[^>]+>", "")
                .replaceAll("[\\r\\n\\t]", " ")
                .replaceAll("\\s+", " ");
    }
    
   
    public String validateName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        String sanitized = sanitizeInput(name);
        if (sanitized.length() < 2 || sanitized.length() > 50) {
            return null;
        }
        
        // only allow letters, spaces, hyphens, and apostrophes
        if (!sanitized.matches("^[a-zA-Z\\s\\-']+$")) {
            return null;
        }
        
        return sanitized;
    }
    
   
    public static class ValidationResult {
        private boolean isValid;
        private List<String> errors;
        
        public ValidationResult() {
            this.isValid = true;
            this.errors = new ArrayList<>();
        }
        
        public void addError(String error) {
            this.isValid = false;
            this.errors.add(error);
        }
        
        public boolean isValid() {
            return isValid;
        }
        
        public List<String> getErrors() {
            return errors;
        }
    }
    
    
    public ValidationResult validateRegistrationData(String email, String phone, String firstName, String lastName, String password) {
        ValidationResult result = new ValidationResult();
        
        if (!isValidEmail(email)) {
            result.addError("invalid email format");
        }
        
        if (!isValidPhone(phone)) {
            result.addError("invalid phone number format");
        }
        
        if (validateName(firstName) == null) {
            result.addError("invalid first name");
        }
        
        if (validateName(lastName) == null) {
            result.addError("invalid last name");
        }
        
        if (!isValidPassword(password)) {
            result.addError("password must be at least 8 characters with uppercase, lowercase, number and special character");
        }
        
        return result;
    }
}
