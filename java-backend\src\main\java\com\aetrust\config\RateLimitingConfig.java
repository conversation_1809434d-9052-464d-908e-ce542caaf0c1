package com.aetrust.config;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Refill;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

@Configuration
public class RateLimitingConfig implements WebMvcConfigurer {

    @Value("${aetrust.security.rate-limit.ip-limit:100}")
    private int ipRateLimit;

    @Value("${aetrust.security.rate-limit.user-limit:50}")
    private int userRateLimit;

    @Value("${aetrust.security.rate-limit.window:3600000}")
    private long rateLimitWindow;

    @Value("${aetrust.feature-flags.rate-limiting.enabled:true}")
    private boolean rateLimitingEnabled;

    private final Map<String, Bucket> ipBuckets = new ConcurrentHashMap<>();
    private final Map<String, Bucket> userBuckets = new ConcurrentHashMap<>();

    @Bean
    public Bucket createIpBucket() {
        Bandwidth limit = Bandwidth.classic(ipRateLimit, Refill.intervally(ipRateLimit, Duration.ofMillis(rateLimitWindow)));
        return Bucket.builder()
            .addLimit(limit)
            .build();
    }

    @Bean
    public Bucket createUserBucket() {
        Bandwidth limit = Bandwidth.classic(userRateLimit, Refill.intervally(userRateLimit, Duration.ofMillis(rateLimitWindow)));
        return Bucket.builder()
            .addLimit(limit)
            .build();
    }

    public Bucket getIpBucket(String ip) {
        return ipBuckets.computeIfAbsent(ip, k -> {
            Bandwidth limit = Bandwidth.classic(ipRateLimit, Refill.intervally(ipRateLimit, Duration.ofMillis(rateLimitWindow)));
            return Bucket.builder().addLimit(limit).build();
        });
    }

    public Bucket getUserBucket(String userId) {
        return userBuckets.computeIfAbsent(userId, k -> {
            Bandwidth limit = Bandwidth.classic(userRateLimit, Refill.intervally(userRateLimit, Duration.ofMillis(rateLimitWindow)));
            return Bucket.builder().addLimit(limit).build();
        });
    }

    public boolean isRateLimitingEnabled() {
        return rateLimitingEnabled;
    }

    // cleanup old buckets periodically
    public void cleanupOldBuckets() {
        // remove buckets that haven't been used recently
        ipBuckets.entrySet().removeIf(entry -> entry.getValue().getAvailableTokens() == ipRateLimit);
        userBuckets.entrySet().removeIf(entry -> entry.getValue().getAvailableTokens() == userRateLimit);
    }
}
