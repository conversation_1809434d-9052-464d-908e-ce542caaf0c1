package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.owasp.encoder.Encode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;
import java.util.List;
import java.util.Arrays;

@Slf4j
@Service
public class InputValidationService {

    @Value("${aetrust.feature-flags.input-validation.enabled:true}")
    private boolean inputValidationEnabled;

    @Value("${aetrust.feature-flags.output-encoding.enabled:true}")
    private boolean outputEncodingEnabled;

    // SQL injection patterns
    private static final List<Pattern> SQL_INJECTION_PATTERNS = Arrays.asList(
        Pattern.compile("('|(\\-\\-)|(;)|(\\|)|(\\*)|(%))", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(union|select|insert|update|delete|drop|create|alter|exec|execute)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(script|javascript|vbscript|onload|onerror|onclick)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(<|>|&lt;|&gt;)", Pattern.CASE_INSENSITIVE)
    );

    // XSS patterns
    private static final List<Pattern> XSS_PATTERNS = Arrays.asList(
        Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("on\\w+\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("<iframe[^>]*>.*?</iframe>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("<object[^>]*>.*?</object>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("<embed[^>]*>.*?</embed>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL)
    );

    // Path traversal patterns
    private static final List<Pattern> PATH_TRAVERSAL_PATTERNS = Arrays.asList(
        Pattern.compile("\\.\\.[\\\\/]", Pattern.CASE_INSENSITIVE),
        Pattern.compile("[\\\\/]\\.\\.[\\\\/]", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%2e%2e[\\\\/]", Pattern.CASE_INSENSITIVE),
        Pattern.compile("[\\\\/]%2e%2e[\\\\/]", Pattern.CASE_INSENSITIVE)
    );

    public ValidationResult validateInput(String input, String fieldName) {
        if (!inputValidationEnabled) {
            return ValidationResult.valid();
        }

        if (input == null) {
            return ValidationResult.valid();
        }

        // check for SQL injection
        if (containsSqlInjection(input)) {
            log.warn("SQL injection attempt detected in field '{}': {}", fieldName, input);
            return ValidationResult.invalid("Invalid characters detected in " + fieldName, "SQL_INJECTION_DETECTED");
        }

        // check for XSS
        if (containsXss(input)) {
            log.warn("XSS attempt detected in field '{}': {}", fieldName, input);
            return ValidationResult.invalid("Invalid script content detected in " + fieldName, "XSS_DETECTED");
        }

        // check for path traversal
        if (containsPathTraversal(input)) {
            log.warn("Path traversal attempt detected in field '{}': {}", fieldName, input);
            return ValidationResult.invalid("Invalid path characters detected in " + fieldName, "PATH_TRAVERSAL_DETECTED");
        }

        return ValidationResult.valid();
    }

    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }

        // remove null bytes
        input = input.replace("\0", "");
        
        // normalize line endings
        input = input.replace("\r\n", "\n").replace("\r", "\n");
        
        // trim whitespace
        input = input.trim();
        
        return input;
    }

    public String encodeForHtml(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forHtml(input);
    }

    public String encodeForHtmlAttribute(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forHtmlAttribute(input);
    }

    public String encodeForJavaScript(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forJavaScript(input);
    }

    public String encodeForUrl(String input) {
        if (!outputEncodingEnabled || input == null) {
            return input;
        }
        return Encode.forUriComponent(input);
    }

    private boolean containsSqlInjection(String input) {
        return SQL_INJECTION_PATTERNS.stream()
            .anyMatch(pattern -> pattern.matcher(input).find());
    }

    private boolean containsXss(String input) {
        return XSS_PATTERNS.stream()
            .anyMatch(pattern -> pattern.matcher(input).find());
    }

    private boolean containsPathTraversal(String input) {
        return PATH_TRAVERSAL_PATTERNS.stream()
            .anyMatch(pattern -> pattern.matcher(input).find());
    }

    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        private final String errorCode;

        private ValidationResult(boolean valid, String message, String errorCode) {
            this.valid = valid;
            this.message = message;
            this.errorCode = errorCode;
        }

        public static ValidationResult valid() {
            return new ValidationResult(true, null, null);
        }

        public static ValidationResult invalid(String message, String errorCode) {
            return new ValidationResult(false, message, errorCode);
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        public String getErrorCode() {
            return errorCode;
        }
    }
}
