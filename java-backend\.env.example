# AeTrust Java Backend Environment Configuration

# Server Configuration
PORT=3000
SPRING_PROFILES_ACTIVE=development

# PostgreSQL Database Configuration
# Primary database connection - update these for your environment
DATABASE_URL=*******************************************
DB_USERNAME=postgres
DB_PASSWORD=password

# Connection Pool Settings (HikariCP)
DB_CONNECTION_POOL_SIZE=20
DB_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
DB_LEAK_DETECTION=60000
DB_VALIDATION_TIMEOUT=5000
DB_INIT_FAIL_TIMEOUT=1

# PostgreSQL Performance Optimizations
DB_CACHE_PREP_STMTS=true
DB_PREP_STMT_CACHE_SIZE=250
DB_PREP_STMT_CACHE_SQL_LIMIT=2048
DB_USE_SERVER_PREP_STMTS=true
DB_USE_LOCAL_SESSION_STATE=true
DB_REWRITE_BATCHED_STATEMENTS=true
DB_CACHE_RESULT_SET_METADATA=true
DB_CACHE_SERVER_CONFIG=true
DB_ELIDE_SET_AUTO_COMMITS=true
DB_MAINTAIN_TIME_STATS=false

# PostgreSQL Connection Settings
DB_APPLICATION_NAME=aetrust-backend
DB_CONNECT_TIMEOUT=10
DB_SOCKET_TIMEOUT=30
DB_TCP_KEEP_ALIVE=true
DB_LOG_UNCLOSED_CONNECTIONS=false

# JPA/Hibernate Settings
DDL_AUTO=validate
FORMAT_SQL=false
SHOW_SQL=false
SQL_COMMENTS=false
BATCH_SIZE=25
FETCH_SIZE=50
USE_L2_CACHE=false
USE_QUERY_CACHE=false
HIBERNATE_STATS=false

# MongoDB Configuration (if still needed for some services)
MONGODB_URI=mongodb://localhost:27017/aetrust
MONGODB_DATABASE=aetrust

# Redis Configuration (Cache System)
# Set REDIS_ENABLED=true to use Redis, false to use in-memory cache
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_MAX_ACTIVE=10
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=1
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-secure
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-make-it-long-and-secure
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Configuration
ENCRYPTION_KEY=your-32-character-encryption-key-here
HASH_SALT_ROUNDS=12

# Security Configuration
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION=900000
SECURITY_RATE_LIMIT_WINDOW=3600000

# Notification Configuration
SMS_PROVIDER=twilio
EMAIL_PROVIDER=sendgrid
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Cloudinary Configuration (Optional - if not set, uses local storage)
CLOUDINARY_ENABLED=false
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
CLOUDINARY_FOLDER=aetrust

# Logging Configuration
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_AETRUST=DEBUG
LOGGING_FILE=logs/aetrust-backend.log

# Development/Production Flags
DEBUG_MODE=true
ENABLE_SWAGGER=true
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CONFIG_CACHE_TTL=300

# Database Migration
FLYWAY_ENABLED=false

# Monitoring & Health Checks
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized

# External API Configuration
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRY_ATTEMPTS=3
EXTERNAL_API_CIRCUIT_BREAKER_ENABLED=true

# Performance Configuration
THREAD_POOL_SIZE=20
CONNECTION_TIMEOUT=30000
READ_TIMEOUT=45000

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSRF_PROTECTION_ENABLED=true
XSS_PROTECTION_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_CAPACITY=200

# Session Configuration
SESSION_TIMEOUT=1800000
SESSION_CLEANUP_INTERVAL=300000

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
