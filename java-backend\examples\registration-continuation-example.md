# Registration Continuation Example

This example demonstrates how to use the new registration continuation endpoints to handle users who stop registration midway and want to continue later.

## Scenario

A user starts registration on their phone but doesn't complete it. Later, they return to the app and want to continue from where they left off.

## Step-by-Step Example

### 1. User Returns and Checks Status

**Request:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/check-status \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response (Registration Found):**
```json
{
  "success": true,
  "message": "registration status retrieved",
  "data": {
    "registrationFound": true,
    "currentStep": "phone_verification",
    "phoneVerified": false,
    "emailVerified": false,
    "completed": false,
    "canContinue": true,
    "nextAction": "verify_phone",
    "message": "registration in progress",
    "requiresLogin": false,
    "requiresRestart": false
  }
}
```

### 2. Get Continuation Details

**Request:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/continue \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration continuation details",
  "data": {
    "registrationId": "reg_abc123def456",
    "currentStep": "phone_verification",
    "nextStep": "email_verification",
    "phoneVerified": false,
    "emailVerified": false,
    "stepInstructions": "verify your phone number with the SMS code sent to you",
    "requiredFields": {
      "required": ["phone", "code"],
      "endpoint": "/auth/registration/verify-phone"
    }
  }
}
```

### 3. Complete Phone Verification

**Request:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/verify-phone \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+250788123456",
    "code": "123456"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "phone verification successful",
  "data": {
    "phoneVerified": true,
    "nextStep": "email_verification",
    "registrationId": "reg_abc123def456"
  }
}
```

### 4. Check Status Again (After Phone Verification)

**Request:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/check-status \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration status retrieved",
  "data": {
    "registrationFound": true,
    "currentStep": "email_verification",
    "phoneVerified": true,
    "emailVerified": false,
    "completed": false,
    "canContinue": true,
    "nextAction": "verify_email",
    "message": "registration in progress",
    "requiresLogin": false,
    "requiresRestart": false
  }
}
```

### 5. Continue with Email Verification

**Get continuation details:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/continue \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration continuation details",
  "data": {
    "registrationId": "reg_abc123def456",
    "currentStep": "email_verification",
    "nextStep": "personal_info",
    "phoneVerified": true,
    "emailVerified": false,
    "stepInstructions": "verify your email address with the code sent to your email",
    "requiredFields": {
      "required": ["email", "code"],
      "endpoint": "/auth/registration/verify-email"
    }
  }
}
```

### 6. Complete Email Verification

**Request:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/verify-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "code": "654321"
  }'
```

### 7. Continue with Personal Info

**Get continuation details:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/continue \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration continuation details",
  "data": {
    "registrationId": "reg_abc123def456",
    "currentStep": "personal_info",
    "nextStep": "transaction_pin",
    "phoneVerified": true,
    "emailVerified": true,
    "stepInstructions": "complete your personal information including name, date of birth, and address",
    "requiredFields": {
      "required": ["registrationId", "firstName", "lastName", "dateOfBirth", "password"],
      "optional": ["gender", "address"],
      "endpoint": "/auth/registration/personal-info"
    }
  }
}
```

**Complete personal info:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/personal-info \
  -H "Content-Type: application/json" \
  -d '{
    "registrationId": "reg_abc123def456",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "gender": "male",
    "address": "123 Main St, Kigali",
    "password": "SecurePass123!"
  }'
```

### 8. Set Transaction PIN (Final Step)

**Get continuation details:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/continue \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration continuation details",
  "data": {
    "registrationId": "reg_abc123def456",
    "currentStep": "transaction_pin",
    "nextStep": "identity_verification",
    "phoneVerified": true,
    "emailVerified": true,
    "stepInstructions": "set up a 4-digit transaction PIN for secure transactions",
    "requiredFields": {
      "required": ["registrationId", "pin", "confirmPin"],
      "endpoint": "/auth/registration/transaction-pin"
    }
  }
}
```

**Set transaction PIN:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/transaction-pin \
  -H "Content-Type: application/json" \
  -d '{
    "registrationId": "reg_abc123def456",
    "pin": "1234",
    "confirmPin": "1234",
    "platform": "web"
  }'
```

### 9. Complete Registration

**Final completion:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/complete \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+250788123456",
    "pin": "1234",
    "confirmPin": "1234",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration completed successfully",
  "data": {
    "registrationCompleted": true,
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userProfile": {
      "id": "123",
      "email": "<EMAIL>",
      "phone": "+250788123456",
      "firstName": "John",
      "lastName": "Doe"
    },
    "walletCreated": true
  }
}
```

## Error Scenarios

### Expired Registration Session

**Request:**
```bash
curl -X POST http://localhost:3000/api/v1/auth/registration/check-status \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "+250788123456",
    "platform": "web"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "registration status retrieved",
  "data": {
    "registrationFound": true,
    "currentStep": "phone_verification",
    "phoneVerified": false,
    "emailVerified": false,
    "completed": false,
    "canContinue": false,
    "nextAction": "restart_registration",
    "message": "registration session expired, please start over",
    "requiresLogin": false,
    "requiresRestart": true
  }
}
```

### No Registration Found

**Response:**
```json
{
  "success": false,
  "message": "no registration found",
  "errorCode": "REGISTRATION_NOT_FOUND"
}
```

### Already Completed Registration

**Response:**
```json
{
  "success": true,
  "message": "registration status retrieved",
  "data": {
    "registrationFound": true,
    "currentStep": "completed",
    "phoneVerified": true,
    "emailVerified": true,
    "completed": true,
    "canContinue": false,
    "nextAction": "login",
    "message": "registration already completed",
    "requiresLogin": true,
    "requiresRestart": false
  }
}
```

## Frontend Integration Tips

1. **Always check status first** before showing registration forms
2. **Handle all response scenarios** (not found, expired, completed, in progress)
3. **Use the provided instructions** to guide users
4. **Show progress indicators** based on current step
5. **Implement proper error handling** for network issues
6. **Cache registration ID** for subsequent requests
7. **Validate inputs** before sending requests
8. **Provide clear user feedback** for each step
