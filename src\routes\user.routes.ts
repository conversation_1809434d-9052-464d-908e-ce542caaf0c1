import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as userController from '../controllers/user.controller';

export async function userRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/me', { preHandler: AuthMiddleware.authenticateRequest }, userController.getCurrentUser as any);
  fastify.put('/profile', { preHandler: AuthMiddleware.authenticateRequest }, userController.updateProfile as any);
  fastify.put('/password', { preHandler: AuthMiddleware.authenticateRequest }, userController.updatePassword as any);
  fastify.post('/profile-picture', { preHandler: AuthMiddleware.authenticateRequest }, userController.uploadProfilePicture as any);
  fastify.delete('/account', { preHandler: AuthMiddleware.authenticateRequest }, userController.deleteAccount as any);
  fastify.get('/search', { preHandler: AuthMiddleware.authenticateRequest }, userController.searchUsers as any);
  fastify.get('/stats', { preHandler: AuthMiddleware.authenticateRequest }, userController.getUserStats as any);
  fastify.get('/verify/:token', userController.verifyEmail as any);
  fastify.get('/dashboard', { preHandler: AuthMiddleware.authenticateRequest }, userController.getUserDashboard as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'user service is running',
      data: { status: 'ok' }
    };
  });
}
