package com.aetrust.models;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

@Entity
@Table(name = "verification_codes", indexes = {
    @Index(name = "idx_user_identifier", columnList = "user_identifier"),
    @Index(name = "idx_verification_type", columnList = "verification_type"),
    @Index(name = "idx_expires_at", columnList = "expires_at"),
    @Index(name = "idx_user_type_expires", columnList = "user_identifier, verification_type, expires_at"),
    @Index(name = "idx_is_used", columnList = "is_used"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerificationCode {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_identifier", nullable = false, length = 100)
    private String userIdentifier; // phone or email
    
    @Column(name = "code", nullable = false, length = 255)
    private String code;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "verification_type", nullable = false, length = 20)
    private VerificationType verificationType;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    @Column(name = "is_used", nullable = false)
    @Builder.Default
    private Boolean isUsed = false;

    @Column(name = "attempts_count", nullable = false)
    @Builder.Default
    private Integer attemptsCount = 0;
    
    @Column(name = "used_at")
    private LocalDateTime usedAt;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    @Column(name = "platform", length = 50)
    private String platform;
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (expiresAt == null) {
            expiresAt = LocalDateTime.now().plusMinutes(10); // 10 minutes default
        }
        if (isUsed == null) {
            isUsed = false;
        }
        if (attemptsCount == null) {
            attemptsCount = 0;
        }
    }
    
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    public boolean isValid() {
        return !isUsed && !isExpired() && attemptsCount < 5;
    }
    
    public void markAsUsed() {
        this.isUsed = true;
        this.usedAt = LocalDateTime.now();
    }
    
    public void incrementAttempts() {
        this.attemptsCount++;
    }
    
    public enum VerificationType {
        SMS,
        EMAIL,
        PHONE,
        PHONE_CALL,
        WHATSAPP
    }
}
