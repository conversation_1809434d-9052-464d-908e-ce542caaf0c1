package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.UploadProfilePictureRequest;
import com.aetrust.utils.CryptoUtils;
import com.cloudinary.Cloudinary;
import com.cloudinary.utils.ObjectUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
public class FileUploadService {

    @Autowired
    private CryptoUtils cryptoUtils;

    @Value("${app.upload.directory:uploads}")
    private String uploadDirectory;

    @Value("${app.upload.max-file-size:10485760}") // 10MB default
    private long maxFileSize;

    @Value("${app.upload.allowed-types:image/jpeg,image/jpg,image/png}")
    private String allowedTypes;

    @Value("${app.cdn.base-url:https://cdn.aetrust.com}")
    private String cdnBaseUrl;

    // Cloudinary configuration
    @Value("${cloudinary.cloud-name:}")
    private String cloudinaryCloudName;

    @Value("${cloudinary.api-key:}")
    private String cloudinaryApiKey;

    @Value("${cloudinary.api-secret:}")
    private String cloudinaryApiSecret;

    @Value("${cloudinary.folder:aetrust}")
    private String cloudinaryFolder;

    @Value("${cloudinary.enabled:false}")
    private boolean cloudinaryEnabled;

    private Cloudinary cloudinary;

    public enum UploadType {
        PROFILE_PICTURE("profilePicture"),
        ID_DOCUMENT_FRONT("idDocumentFront"),
        ID_DOCUMENT_BACK("idDocumentBack"),
        SELFIE_PHOTO("selfiePhoto"),
        DOCUMENTS("documents"),
        CHAT_MEDIA("chatMedia"),
        BUSINESS_DOCUMENT("businessDocument");

        private final String value;

        UploadType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png", "pdf");
    private static final List<String> DANGEROUS_EXTENSIONS = Arrays.asList(
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "php", "asp", "jsp"
    );

    private static final Pattern BASE64_PATTERN = Pattern.compile("^data:([a-zA-Z0-9]+/[a-zA-Z0-9-.+]+);base64,(.*)$");
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UploadResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String fileUrl;
        private String fileName;
        private long fileSize;
        private String publicId;
        private String resourceType;
        private String format;

        public static UploadResult success(String message, String fileUrl, String fileName, long fileSize) {
            return new UploadResult(true, message, null, fileUrl, fileName, fileSize, null, null, null);
        }

        public static UploadResult success(String message, String fileUrl, String fileName, long fileSize,
                                         String publicId, String resourceType, String format) {
            return new UploadResult(true, message, null, fileUrl, fileName, fileSize, publicId, resourceType, format);
        }

        public static UploadResult error(String message, String errorCode) {
            return new UploadResult(false, message, errorCode, null, null, 0, null, null, null);
        }
    }

    public void initializeCloudinary() {
        if (validateCloudinaryConfig()) {
            Map<String, String> config = new HashMap<>();
            config.put("cloud_name", cloudinaryCloudName);
            config.put("api_key", cloudinaryApiKey);
            config.put("api_secret", cloudinaryApiSecret);

            cloudinary = new Cloudinary(config);
            cloudinaryEnabled = true;
            log.info("Cloudinary configuration initialized successfully");
        } else {
            cloudinaryEnabled = false;
            log.info("Cloudinary not configured, using local file storage");
        }
    }

    public boolean validateCloudinaryConfig() {
        return cloudinaryCloudName != null && !cloudinaryCloudName.trim().isEmpty() &&
               cloudinaryApiKey != null && !cloudinaryApiKey.trim().isEmpty() &&
               cloudinaryApiSecret != null && !cloudinaryApiSecret.trim().isEmpty();
    }
    


    public UploadResult uploadBase64File(String base64Data, UploadType uploadType, UUID userId) {
        try {
            if (base64Data == null || base64Data.trim().isEmpty()) {
                return UploadResult.error("No file data provided", "FILE_REQUIRED");
            }

            if (cloudinary == null) {
                initializeCloudinary();
            }

            var matcher = BASE64_PATTERN.matcher(base64Data);
            if (!matcher.matches()) {
                return UploadResult.error("Invalid base64 format", "INVALID_BASE64_FORMAT");
            }

            String mimeType = matcher.group(1);
            String base64Content = matcher.group(2);

            if (!isValidContentTypeForUploadType(mimeType, uploadType)) {
                log.warn("Invalid content type: {} for upload type: {}", mimeType, uploadType);
                return UploadResult.error("Invalid file type", "INVALID_FILE_TYPE");
            }

            byte[] fileBytes;
            try {
                fileBytes = Base64.getDecoder().decode(base64Content);
            } catch (IllegalArgumentException e) {
                return UploadResult.error("Invalid base64 encoding", "INVALID_BASE64_ENCODING");
            }

            if (fileBytes.length > getMaxFileSizeForType(uploadType)) {
                log.warn("File too large: {} bytes for upload type: {}", fileBytes.length, uploadType);
                return UploadResult.error("File too large", "FILE_TOO_LARGE");
            }

            if (!isValidFileContent(fileBytes, mimeType)) {
                log.warn("Invalid file content for upload type: {}", uploadType);
                return UploadResult.error("Invalid file content", "INVALID_FILE_CONTENT");
            }

            if (cloudinaryEnabled) {
                return uploadToCloudinary(fileBytes, uploadType, userId, mimeType);
            } else {
                return uploadToLocalStorage(fileBytes, uploadType, userId, mimeType);
            }

        } catch (Exception e) {
            log.error("Error uploading base64 file for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Upload failed", "INTERNAL_ERROR");
        }
    }

  
    public List<UploadResult> uploadMultipleBase64Files(Map<String, String> base64Files, UploadType uploadType, UUID userId) {
        List<UploadResult> results = new ArrayList<>();

        for (Map.Entry<String, String> entry : base64Files.entrySet()) {
            String fieldName = entry.getKey();
            String base64Data = entry.getValue();

            UploadResult result = uploadBase64File(base64Data, uploadType, userId);
            results.add(result);

            if (!result.isSuccess()) {
                log.warn("Failed to upload file for field: {} for user: {}", fieldName, userId);
            }
        }

        return results;
    }

    public UploadResult uploadProfilePicture(MultipartFile file, UUID userId) {
        try {
            if (file == null || file.isEmpty()) {
                return UploadResult.error("No file provided", "FILE_REQUIRED");
            }
            
            if (file.getSize() > maxFileSize) {
                log.warn("File too large: {} bytes for user: {}", file.getSize(), userId);
                return UploadResult.error("File too large", "FILE_TOO_LARGE");
            }
            
            String contentType = file.getContentType();
            if (!isValidContentType(contentType)) {
                log.warn("Invalid content type: {} for user: {}", contentType, userId);
                return UploadResult.error("Invalid file type", "INVALID_FILE_TYPE");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (!isValidFileExtension(originalFilename)) {
                log.warn("Invalid file extension: {} for user: {}", originalFilename, userId);
                return UploadResult.error("Invalid file extension", "INVALID_FILE_EXTENSION");
            }
            
            if (!isValidImageFile(file)) {
                log.warn("Invalid image file content for user: {}", userId);
                return UploadResult.error("Invalid image file", "INVALID_IMAGE_CONTENT");
            }
            
            String fileExtension = getFileExtension(originalFilename);
            String secureFileName = generateSecureFileName(userId, fileExtension);
            
            Path uploadPath = Paths.get(uploadDirectory, "profile-pictures");
            Files.createDirectories(uploadPath);
            
            Path filePath = uploadPath.resolve(secureFileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            String fileUrl = generateSecureFileUrl("profile-pictures", secureFileName);
            
            log.info("Profile picture uploaded successfully for user: {}, file: {}", 
                userId, secureFileName);
            
            return UploadResult.success("File uploaded successfully", fileUrl, secureFileName, file.getSize());
            
        } catch (IOException e) {
            log.error("Error uploading file for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Failed to upload file", "UPLOAD_FAILED");
        } catch (Exception e) {
            log.error("Unexpected error uploading file for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Upload failed", "INTERNAL_ERROR");
        }
    }
    

    public UploadResult uploadKycDocument(MultipartFile file, UUID userId, String documentType) {
        try {

            if (!isValidKycDocumentType(documentType)) {
                return UploadResult.error("Invalid document type", "INVALID_DOCUMENT_TYPE");
            }
            
            if (file == null || file.isEmpty()) {
                return UploadResult.error("No file provided", "FILE_REQUIRED");
            }
            

            long kycMaxSize = maxFileSize * 2; // 20MB for KYC documents
            if (file.getSize() > kycMaxSize) {
                log.warn("KYC document too large: {} bytes for user: {}", file.getSize(), userId);
                return UploadResult.error("Document too large", "FILE_TOO_LARGE");
            }
            
            String contentType = file.getContentType();
            if (!isValidKycContentType(contentType)) {
                log.warn("Invalid KYC content type: {} for user: {}", contentType, userId);
                return UploadResult.error("Invalid document type", "INVALID_FILE_TYPE");
            }
            
            String fileExtension = getFileExtension(file.getOriginalFilename());
            String secureFileName = generateKycFileName(userId, documentType, fileExtension);
            
            Path uploadPath = Paths.get(uploadDirectory, "kyc-documents", userId.toString());
            Files.createDirectories(uploadPath);
            
            Path filePath = uploadPath.resolve(secureFileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            String fileUrl = generateSecureFileUrl("kyc-documents/" + userId.toString(), secureFileName);
            
            log.info("KYC document uploaded successfully for user: {}, type: {}, file: {}", 
                userId, documentType, secureFileName);
            
            return UploadResult.success("Document uploaded successfully", fileUrl, secureFileName, file.getSize());
            
        } catch (IOException e) {
            log.error("Error uploading KYC document for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Failed to upload document", "UPLOAD_FAILED");
        } catch (Exception e) {
            log.error("Unexpected error uploading KYC document for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Upload failed", "INTERNAL_ERROR");
        }
    }
    

    private boolean isValidContentType(String contentType) {
        if (contentType == null) return false;
        List<String> allowed = Arrays.asList(allowedTypes.split(","));
        return allowed.contains(contentType.toLowerCase());
    }
    

    private boolean isValidKycContentType(String contentType) {
        if (contentType == null) return false;
        return contentType.equals("application/pdf") || 
               contentType.equals("image/jpeg") || 
               contentType.equals("image/jpg") || 
               contentType.equals("image/png");
    }
    
 
    private boolean isValidFileExtension(String filename) {
        if (filename == null) return false;
        
        String extension = getFileExtension(filename).toLowerCase();
        
        if (DANGEROUS_EXTENSIONS.contains(extension)) {
            return false;
        }
        
        return ALLOWED_EXTENSIONS.contains(extension);
    }
    

    private boolean isValidKycDocumentType(String documentType) {
        List<String> validTypes = Arrays.asList(
            "passport", "national_id", "drivers_license", "utility_bill", 
            "bank_statement", "business_license", "tax_certificate"
        );
        return validTypes.contains(documentType);
    }
    

    private boolean isValidImageFile(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes();
            if (fileBytes.length < 4) return false;
            
            // Check JPEG magic bytes
            if (fileBytes[0] == (byte) 0xFF && fileBytes[1] == (byte) 0xD8) {
                return true;
            }
            
            // Check PNG magic bytes
            if (fileBytes[0] == (byte) 0x89 && fileBytes[1] == (byte) 0x50 && 
                fileBytes[2] == (byte) 0x4E && fileBytes[3] == (byte) 0x47) {
                return true;
            }
            
            return false;
        } catch (IOException e) {
            log.error("Error reading file bytes: {}", e.getMessage());
            return false;
        }
    }
    

    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }
    
 
    private String generateSecureFileName(UUID userId, String extension) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomToken = cryptoUtils.generateSecureToken(8);
        return String.format("profile_%s_%s_%s.%s", userId.toString(), timestamp, randomToken, extension);
    }
    
    private String generateKycFileName(UUID userId, String documentType, String extension) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomToken = cryptoUtils.generateSecureToken(8);
        return String.format("kyc_%s_%s_%s_%s.%s", documentType, userId.toString(), timestamp, randomToken, extension);
    }
    

    private UploadResult uploadToCloudinary(byte[] fileBytes, UploadType uploadType, UUID userId, String mimeType) {
        try {
            String folder = getCloudinaryFolderForType(uploadType);

            Map<String, Object> uploadParams = ObjectUtils.asMap(
                "folder", folder,
                "resource_type", "auto",
                "allowed_formats", Arrays.asList("jpg", "png", "jpeg", "gif", "webp", "pdf"),
                "transformation", getTransformationForType(uploadType)
            );

            Map uploadResult = cloudinary.uploader().upload(fileBytes, uploadParams);

            String secureUrl = (String) uploadResult.get("secure_url");
            String publicId = (String) uploadResult.get("public_id");
            String resourceType = (String) uploadResult.get("resource_type");
            String format = (String) uploadResult.get("format");
            Integer bytes = (Integer) uploadResult.get("bytes");

            log.info("File uploaded to Cloudinary successfully for user: {}, public_id: {}", userId, publicId);

            return UploadResult.success("File uploaded successfully", secureUrl, publicId,
                                     bytes != null ? bytes.longValue() : fileBytes.length,
                                     publicId, resourceType, format);

        } catch (Exception e) {
            log.error("Error uploading to Cloudinary for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Cloudinary upload failed", "CLOUDINARY_UPLOAD_FAILED");
        }
    }

    private UploadResult uploadToLocalStorage(byte[] fileBytes, UploadType uploadType, UUID userId, String mimeType) {
        try {
            String fileExtension = getExtensionFromMimeType(mimeType);
            String secureFileName = generateSecureFileName(userId, uploadType, fileExtension);

            Path uploadPath = Paths.get(uploadDirectory, getLocalDirectoryForType(uploadType));
            Files.createDirectories(uploadPath);

            Path filePath = uploadPath.resolve(secureFileName);
            Files.write(filePath, fileBytes);

            String fileUrl = generateSecureFileUrl(getLocalDirectoryForType(uploadType), secureFileName);

            log.info("File uploaded to local storage successfully for user: {}, file: {}", userId, secureFileName);

            return UploadResult.success("File uploaded successfully", fileUrl, secureFileName, fileBytes.length);

        } catch (IOException e) {
            log.error("Error uploading to local storage for user: {}: {}", userId, e.getMessage());
            return UploadResult.error("Local upload failed", "LOCAL_UPLOAD_FAILED");
        }
    }

    private String getCloudinaryFolderForType(UploadType uploadType) {
        String baseFolder = cloudinaryFolder;
        switch (uploadType) {
            case PROFILE_PICTURE:
                return baseFolder + "/profile_pictures";
            case ID_DOCUMENT_FRONT:
            case ID_DOCUMENT_BACK:
                return baseFolder + "/kyc/id_documents";
            case SELFIE_PHOTO:
                return baseFolder + "/kyc/selfies";
            case BUSINESS_DOCUMENT:
                return baseFolder + "/business/documents";
            case DOCUMENTS:
                return baseFolder + "/documents";
            case CHAT_MEDIA:
                return baseFolder + "/chat_media";
            default:
                return baseFolder + "/misc";
        }
    }

    private String getLocalDirectoryForType(UploadType uploadType) {
        switch (uploadType) {
            case PROFILE_PICTURE:
                return "profile-pictures";
            case ID_DOCUMENT_FRONT:
            case ID_DOCUMENT_BACK:
                return "kyc-documents";
            case SELFIE_PHOTO:
                return "kyc-selfies";
            case BUSINESS_DOCUMENT:
                return "business-documents";
            case DOCUMENTS:
                return "documents";
            case CHAT_MEDIA:
                return "chat-media";
            default:
                return "misc";
        }
    }


    private List<Map<String, Object>> getTransformationForType(UploadType uploadType) {
        switch (uploadType) {
            case PROFILE_PICTURE:
            case SELFIE_PHOTO:
                return Arrays.asList(
                    ObjectUtils.asMap("width", 400, "height", 400, "crop", "fill", "gravity", "face"),
                    ObjectUtils.asMap("quality", "auto:good", "format", "auto")
                );
            case ID_DOCUMENT_FRONT:
            case ID_DOCUMENT_BACK:
            case BUSINESS_DOCUMENT:
                return Arrays.asList(
                    ObjectUtils.asMap("width", 1200, "height", 800, "crop", "limit"),
                    ObjectUtils.asMap("quality", "auto:best", "format", "auto")
                );
            default:
                return Arrays.asList(ObjectUtils.asMap("quality", "auto:good", "format", "auto"));
        }
    }

 
    private long getMaxFileSizeForType(UploadType uploadType) {
        switch (uploadType) {
            case ID_DOCUMENT_FRONT:
            case ID_DOCUMENT_BACK:
            case BUSINESS_DOCUMENT:
            case DOCUMENTS:
                return maxFileSize * 2; // 20MB for documents
            default:
                return maxFileSize; // 10MB for images
        }
    }


    private boolean isValidContentTypeForUploadType(String contentType, UploadType uploadType) {
        if (contentType == null) return false;

        switch (uploadType) {
            case PROFILE_PICTURE:
            case SELFIE_PHOTO:
                return contentType.equals("image/jpeg") ||
                       contentType.equals("image/jpg") ||
                       contentType.equals("image/png");
            case ID_DOCUMENT_FRONT:
            case ID_DOCUMENT_BACK:
            case BUSINESS_DOCUMENT:
            case DOCUMENTS:
                return contentType.equals("image/jpeg") ||
                       contentType.equals("image/jpg") ||
                       contentType.equals("image/png") ||
                       contentType.equals("application/pdf");
            default:
                return isValidContentType(contentType);
        }
    }

    private String getExtensionFromMimeType(String mimeType) {
        switch (mimeType) {
            case "image/jpeg":
            case "image/jpg":
                return "jpg";
            case "image/png":
                return "png";
            case "application/pdf":
                return "pdf";
            default:
                return "bin";
        }
    }


    private String generateSecureFileName(UUID userId, UploadType uploadType, String extension) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomToken = cryptoUtils.generateSecureToken(8);
        return String.format("%s_%s_%s_%s.%s",
                           uploadType.getValue(), userId.toString(), timestamp, randomToken, extension);
    }


    private boolean isValidFileContent(byte[] fileBytes, String mimeType) {
        if (fileBytes.length < 4) return false;

        switch (mimeType) {
            case "image/jpeg":
            case "image/jpg":
                return fileBytes[0] == (byte) 0xFF && fileBytes[1] == (byte) 0xD8;
            case "image/png":
                return fileBytes[0] == (byte) 0x89 && fileBytes[1] == (byte) 0x50 &&
                       fileBytes[2] == (byte) 0x4E && fileBytes[3] == (byte) 0x47;
            case "application/pdf":
                return fileBytes[0] == (byte) 0x25 && fileBytes[1] == (byte) 0x50 &&
                       fileBytes[2] == (byte) 0x44 && fileBytes[3] == (byte) 0x46;
            default:
                return true; // Allow other types for now
        }
    }

    private String generateSecureFileUrl(String directory, String filename) {
        return String.format("%s/%s/%s", cdnBaseUrl, directory, filename);
    }
}
