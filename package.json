{"name": "aetrust-backend", "version": "1.0.0", "description": "AeTrust Fintech Platform Backend - Production-grade TypeScript API with MongoDB", "main": "dist/server.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "security:check": "npm audit --audit-level=moderate", "migrate:development": "echo 'Running development migrations...'", "migrate:staging": "echo 'Running staging migrations...'", "migrate:production": "echo 'Running production migrations...'", "db:seed": "ts-node src/scripts/seed.ts", "db:migrate": "ts-node src/scripts/migrate.ts"}, "keywords": ["fintech", "payments", "wallet", "remittance", "lending", "typescript", "fastify", "mongodb"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@fastify/cookie": "^9.2.0", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^7.2.4", "@fastify/multipart": "^8.0.0", "@fastify/rate-limit": "^9.0.1", "@fastify/static": "^6.12.0", "@types/bcrypt": "^6.0.0", "axios": "^1.6.2", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "cloudinary-build-url": "^0.2.4", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "fastify": "^4.24.3", "geoip-lite": "^1.4.10", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "maxmind": "^5.0.0", "moment": "^2.29.4", "mongoose": "^8.0.3", "nodemailer": "^6.10.1", "qrcode": "^1.5.3", "redis": "^4.6.11", "speakeasy": "^2.0.0", "twilio": "^4.23.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/geoip-lite": "^1.4.4", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.19", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "mongodb-memory-server": "^8.13.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^7.1.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/tests", "<rootDir>/src"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"], "testMatch": ["**/__tests__/**/*.test.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/types/**", "!src/**/*.interface.ts", "!src/server.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html", "cobertura"], "testTimeout": 60000, "verbose": true, "forceExit": true, "clearMocks": true, "resetMocks": true, "restoreMocks": true, "maxWorkers": 1, "detectOpenHandles": true}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}