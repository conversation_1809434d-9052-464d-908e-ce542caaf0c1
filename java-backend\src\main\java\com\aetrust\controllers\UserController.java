package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.UserService;
import com.aetrust.services.SecurityService;
import com.aetrust.services.ValidationService;
import com.aetrust.utils.ControllerUtils;
import com.aetrust.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/users")
@Validated
public class UserController {
    
    @Autowired
    private UserService userService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private Environment environment;

    @Autowired
    private ValidationService validationService;

    @Autowired
    private ControllerUtils controllerUtils;

    @Value("${aetrust.security.max-login-attempts:5}")
    private int maxLoginAttempts;

    @Value("${aetrust.file.upload.max-size:10MB}")
    private String maxFileSize;

    @Value("${ENVIRONMENT:development}")
    private String environmentName;
    
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentUser(
            Authentication authentication,
            HttpServletRequest request) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            if (userPayload == null || userPayload.getId() == null) {
                return ResponseEntity.status(401).body(
                    ApiResponse.error("Access denied", "ACCESS_DENIED"));
            }

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                log.warn("User profile not found for ID: {}", userPayload.getId());
                return ResponseEntity.status(404).body(
                    ApiResponse.error("Resource not found", "NOT_FOUND"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> userData = (Map<String, Object>) result.getData();
            return ResponseEntity.ok(
                ApiResponse.success("user profile retrieved successfully", userData));

        } catch (Exception error) {
            log.error("Error fetching user profile: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch user profile", "INTERNAL_ERROR"));
        }
    }
    
    @PutMapping("/profile")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updateProfile(
            @Valid @RequestBody UpdateProfileRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = controllerUtils.getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/profile");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.ProfileUpdateResult result = userService.updateUserProfile(userPayload.getId(), request);

            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("profileUpdated", true);
            responseData.put("updatedFields", result.getData());
            
            return ResponseEntity.ok(
                ApiResponse.success("profile updated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error updating profile: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("profile update failed", "INTERNAL_ERROR"));
        }
    }
    
    @PutMapping("/password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> updatePassword(
            @Valid @RequestBody UpdatePasswordRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = controllerUtils.getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/password");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.PasswordUpdateResult result = userService.updatePassword(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                log.warn("Password update failed for user: {}: {}", userPayload.getId(), result.getMessage());
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Operation failed", "OPERATION_FAILED"));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("passwordUpdated", true);
            responseData.put("requiresReauth", true);
            
            return ResponseEntity.ok(
                ApiResponse.success("password updated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error updating password: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("password update failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/profile-picture")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadProfilePicture(
            @Valid @RequestBody UploadProfilePictureRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                userPayload.getId(), "user", "/users/profile-picture");

            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("Too many upload attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            UserService.UploadResult result = userService.uploadProfilePicture(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("profilePictureUploaded", true);
            responseData.put("profilePictureUrl", result.getFileUrl());
            
            return ResponseEntity.ok(
                ApiResponse.success("profile picture uploaded successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error uploading profile picture: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("profile picture upload failed", "INTERNAL_ERROR"));
        }
    }
    
    @DeleteMapping("/account")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deleteAccount(
            @Valid @RequestBody DeleteAccountRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        
        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();
            String ipAddress = controllerUtils.getClientIp(httpRequest);
            
            SecurityService.ActivityContext activity = new SecurityService.ActivityContext(
                ipAddress, httpRequest.getHeader("User-Agent"), System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity(userPayload.getId(), activity);
            
            if (suspiciousResult.getRiskScore() > 30) {
                return ResponseEntity.status(403).body(
                    ApiResponse.error("account deletion blocked due to suspicious activity", 
                        "SUSPICIOUS_ACTIVITY"));
            }
            
            UserService.DeletionResult result = userService.deleteAccount(
                userPayload.getId(), request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("accountDeleted", true);
            responseData.put("deletionScheduled", result.isDeletionScheduled());
            responseData.put("finalDeletionDate", result.getFinalDeletionDate());
            
            return ResponseEntity.ok(
                ApiResponse.success("account deletion initiated", responseData));
                
        } catch (Exception error) {
            log.error("Error deleting account: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("account deletion failed", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStats(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("user not found", "USER_NOT_FOUND"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> userData = (Map<String, Object>) result.getData();
            Map<String, Object> statsData = new HashMap<>();

            statsData.put("totalTransactions", 0);
            statsData.put("totalBalance", "0.00");
            statsData.put("accountAge", calculateAccountAge(userData));
            statsData.put("verificationLevel", calculateVerificationLevel(userData));

            return ResponseEntity.ok(
                ApiResponse.success("user stats retrieved successfully", statsData));

        } catch (Exception error) {
            log.error("Error fetching user stats: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to fetch user stats", "INTERNAL_ERROR"));
        }
    }

    @GetMapping("/verify/{token}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyEmail(
            @PathVariable String token,
            HttpServletRequest httpRequest) {

        try {
            // TODO implement email veri
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("emailVerified", true);
            responseData.put("message", "email verification successful");

            return ResponseEntity.ok(
                ApiResponse.success("email verified successfully", responseData));

        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("email verification failed", "INTERNAL_ERROR"));
        }
    }

    /**
     * Comprehensive dashboard endpoint that returns all user data including:
     * - User profile and account information
     * - Wallet data and financial summary
     * - Security metrics and settings
     * - Activity summary and recent transactions
     * - Account metrics and completeness scores
     * - Enhanced analytics and insights
     */
    @GetMapping("/dashboard")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserDashboard(
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            UserService.CompleteUserResult result = userService.getCompleteUserById(userPayload.getId());

            if (!result.isSuccess()) {
                return ResponseEntity.status(404).body(
                    ApiResponse.error("Dashboard data not available", "NOT_FOUND"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> userData = (Map<String, Object>) result.getData();

            Map<String, Object> dashboardData = createComprehensiveDashboardData(userData, userPayload.getId());

            return ResponseEntity.ok(
                ApiResponse.success("Dashboard data retrieved successfully", dashboardData));

        } catch (Exception error) {
            log.error("Error fetching dashboard data: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("Failed to load dashboard", "INTERNAL_ERROR"));
        }
    }





    private int calculateAccountAge(Map<String, Object> userData) {
        return 30; // placeholder
    }

    private String calculateVerificationLevel(Map<String, Object> userData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> user = (Map<String, Object>) userData.get("user");
        @SuppressWarnings("unchecked")
        Map<String, Object> identity = (Map<String, Object>) userData.get("identity");

        if (identity != null && Boolean.TRUE.equals(identity.get("isVerified"))) {
            return "VERIFIED";
        } else if (Boolean.TRUE.equals(user.get("isVerified"))) {
            return "BASIC";
        }
        return "UNVERIFIED";
    }

    private Map<String, Object> createDashboardData(Map<String, Object> userData) {
        Map<String, Object> dashboard = new HashMap<>();
        @SuppressWarnings("unchecked")
        Map<String, Object> user = (Map<String, Object>) userData.get("user");
        @SuppressWarnings("unchecked")
        Map<String, Object> profile = (Map<String, Object>) userData.get("profile");

        dashboard.put("user", user);
        dashboard.put("profile", profile);
        dashboard.put("quickStats", createQuickStats(userData));
        dashboard.put("recentActivity", createRecentActivity());

        return dashboard;
    }



    private Map<String, Object> createQuickStats(Map<String, Object> userData) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalBalance", "0.00");
        stats.put("totalTransactions", 0);
        stats.put("pendingTransactions", 0);
        stats.put("accountAge", calculateAccountAge(userData));

        return stats;
    }

    private Map<String, Object> createRecentActivity() {
        Map<String, Object> activity = new HashMap<>();
        activity.put("transactions", new ArrayList<>());
        activity.put("logins", new ArrayList<>());
        activity.put("updates", new ArrayList<>());

        return activity;
    }

    private Map<String, Object> createAnalytics(Map<String, Object> userData) {
        Map<String, Object> analytics = new HashMap<>();
        analytics.put("spendingPattern", new HashMap<>());
        analytics.put("transactionTrends", new HashMap<>());
        analytics.put("monthlyStats", new HashMap<>());

        return analytics;
    }

    private Map<String, Object> createRecommendations() {
        Map<String, Object> recommendations = new HashMap<>();
        recommendations.put("security", new ArrayList<>());
        recommendations.put("features", new ArrayList<>());
        recommendations.put("offers", new ArrayList<>());

        return recommendations;
    }

    private Map<String, Object> createNotifications() {
        Map<String, Object> notifications = new HashMap<>();
        notifications.put("unread", 0);
        notifications.put("recent", new ArrayList<>());
        notifications.put("important", new ArrayList<>());

        return notifications;
    }

    private Map<String, Object> createWalletSummary(Map<String, Object> userData) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalBalance", "0.00");
        summary.put("availableBalance", "0.00");
        summary.put("pendingBalance", "0.00");
        summary.put("walletCount", 1);

        return summary;
    }

    private String calculateSecurityLevel(Map<String, Object> security) {
        if (security == null) return "LOW";

        int score = 0;
        if (Boolean.TRUE.equals(security.get("twoFactorEnabled"))) score += 30;
        if (Boolean.TRUE.equals(security.get("biometricEnabled"))) score += 25;
        if (Boolean.TRUE.equals(security.get("transactionPinSet"))) score += 20;

        if (score >= 70) return "HIGH";
        if (score >= 40) return "MEDIUM";
        return "LOW";
    }

 
    private boolean isValidSearchQuery(String query) {
        if (query == null || query.trim().isEmpty()) {
            return false;
        }
        String sanitizedQuery = validationService.sanitizeInput(query.trim());
        if (sanitizedQuery.length() > 100) {
            return false;
        }
        if (sanitizedQuery.matches(".*[<>\"'%;()&|\\\\].*")) {
            return false;
        }
        if (sanitizedQuery.toLowerCase().contains("script") ||
            sanitizedQuery.toLowerCase().contains("javascript") ||
            sanitizedQuery.toLowerCase().contains("vbscript")) {
            return false;
        }

        return true;
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Map<String, Object>>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit,
            Authentication authentication,
            HttpServletRequest httpRequest) {

        try {
            JwtUtils.UserPayload userPayload = (JwtUtils.UserPayload) authentication.getPrincipal();

            if (userPayload.getRole() == null ||
                (!userPayload.getRole().equals("ADMIN") && !userPayload.getRole().equals("SUPPORT"))) {
                log.warn("Unauthorized search attempt by user: {} with role: {}",
                    userPayload.getId(), userPayload.getRole());
                return ResponseEntity.status(403).body(
                    ApiResponse.error("Access denied", "ACCESS_DENIED"));
            }

            if (!isValidSearchQuery(query)) {
                log.warn("Invalid search query from user: {}: {}",
                    userPayload.getId(), validationService.sanitizeInput(query));
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("Invalid search query", "INVALID_QUERY"));
            }

            if (page < 1) page = 1;
            if (limit > 100) limit = 100;
            if (limit < 1) limit = 20;

            log.info("User search performed by admin: {}, query: {}, page: {}, limit: {}",
                userPayload.getId(), validationService.sanitizeInput(query), page, limit);

            // Search implementation placeholder - would integrate with UserService
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("users", new ArrayList<>());
            responseData.put("pagination", Map.of(
                "currentPage", page,
                "totalPages", 0,
                "totalResults", 0,
                "resultsPerPage", limit
            ));
            responseData.put("searchQuery", validationService.sanitizeInput(query));
            responseData.put("message", "User search service integration pending");

            return ResponseEntity.ok(
                ApiResponse.success("User search completed", responseData));

        } catch (Exception error) {
            log.error("Error searching users: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("User search failed", "INTERNAL_ERROR"));
        }
    }

    private Map<String, Object> createComprehensiveDashboardData(Map<String, Object> userData, String userId) {
        Map<String, Object> dashboardData = new HashMap<>();

        dashboardData.put("user", createUserInfo(userData));
        dashboardData.put("profile", createProfileInfo(userData));
        dashboardData.put("security", createSecurityInfo(userData));
        dashboardData.put("wallet", createWalletSummary(userData));
        dashboardData.put("activity", createActivityData(userId));
        dashboardData.put("metrics", createAccountMetrics(userData));
        dashboardData.put("insights", createInsights(userData));
        dashboardData.put("quickStats", createQuickStats(userData));
        dashboardData.put("recentActivity", createRecentActivity());
        dashboardData.put("timestamp", LocalDateTime.now());
        dashboardData.put("dataVersion", "v1.0");
        dashboardData.put("lastUpdated", LocalDateTime.now());

        return dashboardData;
    }

   
    private Map<String, Object> createUserInfo(Map<String, Object> userData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> user = (Map<String, Object>) userData.get("user");

        Map<String, Object> userInfo = new HashMap<>();
        if (user != null) {
            userInfo.put("id", user.get("id"));
            userInfo.put("email", user.get("email"));
            userInfo.put("phone", user.get("phone"));
            userInfo.put("role", user.get("role"));
            userInfo.put("accountStatus", user.get("accountStatus"));
            userInfo.put("isVerified", user.get("isVerified"));
            userInfo.put("createdAt", user.get("createdAt"));
            userInfo.put("lastLogin", user.get("lastLogin"));
        }
        return userInfo;
    }

  
    private Map<String, Object> createProfileInfo(Map<String, Object> userData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> profile = (Map<String, Object>) userData.get("profile");

        Map<String, Object> profileInfo = new HashMap<>();
        if (profile != null) {
            profileInfo.put("firstName", profile.get("firstName"));
            profileInfo.put("lastName", profile.get("lastName"));
            profileInfo.put("dateOfBirth", profile.get("dateOfBirth"));
            profileInfo.put("profilePicture", profile.get("profilePicture"));
            profileInfo.put("bio", profile.get("bio"));
            profileInfo.put("address", profile.get("address"));
            profileInfo.put("nationality", profile.get("nationality"));
        }
        return profileInfo;
    }

  
    private Map<String, Object> createSecurityInfo(Map<String, Object> userData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> security = (Map<String, Object>) userData.get("security");

        Map<String, Object> securityInfo = new HashMap<>();
        if (security != null) {
            securityInfo.put("twoFactorEnabled", security.get("twoFactorEnabled"));
            securityInfo.put("biometricEnabled", security.get("biometricEnabled"));
            securityInfo.put("transactionPinSet", security.get("transactionPinSet"));
            securityInfo.put("securityLevel", calculateSecurityLevel(security));
            securityInfo.put("lastPasswordChange", security.get("lastPasswordChange"));
            securityInfo.put("loginDevices", security.get("loginDevices"));
        }
        return securityInfo;
    }

 
    private Map<String, Object> createActivityData(String userId) {
        Map<String, Object> activityData = new HashMap<>();

        activityData.put("recentTransactions", new ArrayList<>());
        activityData.put("loginHistory", new ArrayList<>());
        activityData.put("securityEvents", new ArrayList<>());
        activityData.put("lastActivity", LocalDateTime.now());
        activityData.put("timestamp", LocalDateTime.now());

        return activityData;
    }

   
    private Map<String, Object> createAccountMetrics(Map<String, Object> userData) {
        Map<String, Object> metrics = new HashMap<>();

        metrics.put("accountAge", calculateAccountAge(userData));
        metrics.put("profileCompleteness", calculateProfileCompleteness(userData));
        metrics.put("securityScore", calculateSecurityScore(userData));
        metrics.put("verificationLevel", calculateVerificationLevel(userData));
        metrics.put("totalTransactions", 0); 
        metrics.put("totalSpent", "0.00");
        metrics.put("averageTransactionAmount", "0.00");

        return metrics;
    }

  
    private Map<String, Object> createInsights(Map<String, Object> userData) {
        Map<String, Object> insights = new HashMap<>();

        List<String> recommendations = new ArrayList<>();
        List<String> alerts = new ArrayList<>();

        @SuppressWarnings("unchecked")
        Map<String, Object> security = (Map<String, Object>) userData.get("security");
        if (security != null) {
            if (!Boolean.TRUE.equals(security.get("twoFactorEnabled"))) {
                recommendations.add("Enable two-factor authentication for enhanced security");
            }
            if (!Boolean.TRUE.equals(security.get("biometricEnabled"))) {
                recommendations.add("Set up biometric authentication for faster login");
            }
        }

        int completeness = calculateProfileCompleteness(userData);
        if (completeness < 80) {
            recommendations.add("Complete your profile to unlock all features");
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> identity = (Map<String, Object>) userData.get("identity");
        if (identity == null || !"VERIFIED".equals(identity.get("verificationStatus"))) {
            recommendations.add("Complete KYC verification to increase transaction limits");
        }

        insights.put("recommendations", recommendations);
        insights.put("alerts", alerts);
        insights.put("riskLevel", "LOW");
        insights.put("accountHealth", "GOOD");

        return insights;
    }

 
    private int calculateProfileCompleteness(Map<String, Object> userData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> profile = (Map<String, Object>) userData.get("profile");
        @SuppressWarnings("unchecked")
        Map<String, Object> user = (Map<String, Object>) userData.get("user");

        int totalFields = 10;
        int completedFields = 0;

        if (user != null) {
            if (user.get("email") != null) completedFields++;
            if (user.get("phone") != null) completedFields++;
        }

        if (profile != null) {
            if (profile.get("firstName") != null) completedFields++;
            if (profile.get("lastName") != null) completedFields++;
            if (profile.get("dateOfBirth") != null) completedFields++;
            if (profile.get("profilePicture") != null) completedFields++;
            if (profile.get("bio") != null) completedFields++;
            if (profile.get("address") != null) completedFields++;
            if (profile.get("nationality") != null) completedFields++;
            if (profile.get("occupation") != null) completedFields++;
        }

        return (completedFields * 100) / totalFields;
    }

    
    private int calculateSecurityScore(Map<String, Object> userData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> security = (Map<String, Object>) userData.get("security");

        int score = 0;
        int maxScore = 100;

        if (security != null) {
            if (Boolean.TRUE.equals(security.get("twoFactorEnabled"))) score += 30;
            if (Boolean.TRUE.equals(security.get("biometricEnabled"))) score += 25;
            if (Boolean.TRUE.equals(security.get("transactionPinSet"))) score += 20;
            if (security.get("lastPasswordChange") != null) score += 15;
            if (security.get("loginDevices") != null) score += 10;
        }

        return Math.min(score, maxScore);
    }
}
