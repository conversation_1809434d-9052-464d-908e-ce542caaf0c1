import axios from 'axios';
import { logger } from '../config/logger';
import { WalletService } from './wallet.service';
import { Currency, TransactionType } from '../types';

export class SecureWalletService {
  private static readonly JAVA_SECURITY_SERVICE_URL = process.env.JAVA_SECURITY_SERVICE_URL || 'http://localhost:8081/api/v1';
  private static readonly REQUEST_TIMEOUT = 15000;

  static async transferBetweenWallets(data: {
    senderId: string;
    fromWalletId: string;
    toWalletId: string;
    amount: number;
    currency: Currency;
    description?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }) {
    try {
      // call java security service for secure transfer processing
      const response = await axios.post(`${this.JAVA_SECURITY_SERVICE_URL}/wallet/transfer`, {
        senderId: data.senderId,
        fromWalletId: data.fromWalletId,
        toWalletId: data.toWalletId,
        amount: data.amount,
        currency: data.currency,
        description: data.description,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        metadata: data.metadata
      }, {
        timeout: this.REQUEST_TIMEOUT,
        headers: {
          'Content-Type': 'application/json',
          'X-Service-Source': 'typescript-backend'
        }
      });

      if (response.data.success) {
        logger.info('Transfer completed successfully via Java security service', {
          senderId: data.senderId,
          amount: data.amount,
          currency: data.currency,
          transferId: response.data.data?.transferId
        });
        
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        logger.warn('Transfer failed via Java service', {
          senderId: data.senderId,
          message: response.data.message
        });
        
        return {
          success: false,
          message: response.data.message || 'transfer failed',
          errorCode: response.data.errorCode
        };
      }
    } catch (error: any) {
      logger.error('Java security service error during transfer:', {
        error: error.response?.data || error.message,
        senderId: data.senderId,
        amount: data.amount,
        status: error.response?.status
      });
      
      // fallback to local wallet service if java service is down
      if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
        logger.warn('Java service unavailable, falling back to local wallet service');
        return await WalletService.transferBetweenWallets({
          fromWalletId: data.fromWalletId,
          toWalletId: data.toWalletId,
          amount: data.amount,
          description: data.description,
          metadata: data.metadata
        });
      }
      
      return {
        success: false,
        message: 'transfer service temporarily unavailable'
      };
    }
  }

  static async creditWallet(data: {
    walletId: string;
    amount: number;
    description?: string;
    transactionType?: TransactionType;
    externalRef?: string;
    metadata?: any;
  }) {
    try {
      const response = await axios.post(`${this.JAVA_SECURITY_SERVICE_URL}/wallet/credit`, {
        walletId: data.walletId,
        amount: data.amount,
        description: data.description,
        transactionType: data.transactionType,
        externalRef: data.externalRef,
        metadata: data.metadata
      }, {
        timeout: this.REQUEST_TIMEOUT,
        headers: {
          'Content-Type': 'application/json',
          'X-Service-Source': 'typescript-backend'
        }
      });

      if (response.data.success) {
        logger.info('Wallet credited successfully via Java security service', {
          walletId: data.walletId,
          amount: data.amount,
          transactionId: response.data.data?.transaction?.id
        });
        
        return response.data;
      } else {
        return {
          success: false,
          message: response.data.message || 'wallet credit failed'
        };
      }
    } catch (error: any) {
      logger.error('Java security service error during wallet credit:', {
        error: error.response?.data || error.message,
        walletId: data.walletId,
        amount: data.amount,
        status: error.response?.status
      });
      
      // fallback to local wallet service
      if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
        logger.warn('Java service unavailable, falling back to local wallet service');
        return await WalletService.creditWallet(data);
      }
      
      return {
        success: false,
        message: 'wallet credit service temporarily unavailable'
      };
    }
  }

  static async debitWallet(data: {
    walletId: string;
    amount: number;
    description?: string;
    transactionType?: TransactionType;
    externalRef?: string;
    metadata?: any;
  }) {
    try {
      const response = await axios.post(`${this.JAVA_SECURITY_SERVICE_URL}/wallet/debit`, {
        walletId: data.walletId,
        amount: data.amount,
        description: data.description,
        transactionType: data.transactionType,
        externalRef: data.externalRef,
        metadata: data.metadata
      }, {
        timeout: this.REQUEST_TIMEOUT,
        headers: {
          'Content-Type': 'application/json',
          'X-Service-Source': 'typescript-backend'
        }
      });

      if (response.data.success) {
        logger.info('Wallet debited successfully via Java security service', {
          walletId: data.walletId,
          amount: data.amount,
          transactionId: response.data.data?.transaction?.id
        });
        
        return response.data;
      } else {
        return {
          success: false,
          message: response.data.message || 'wallet debit failed'
        };
      }
    } catch (error: any) {
      logger.error('Java security service error during wallet debit:', {
        error: error.response?.data || error.message,
        walletId: data.walletId,
        amount: data.amount,
        status: error.response?.status
      });
      
      // fallback to local wallet service
      if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
        logger.warn('Java service unavailable, falling back to local wallet service');
        return await WalletService.debitWallet(data);
      }
      
      return {
        success: false,
        message: 'wallet debit service temporarily unavailable'
      };
    }
  }

  static async getWalletBalance(walletId: string) {
    try {
      const response = await axios.get(`${this.JAVA_SECURITY_SERVICE_URL}/wallet/${walletId}/balance`, {
        timeout: 5000,
        headers: {
          'X-Service-Source': 'typescript-backend'
        }
      });

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'failed to get wallet balance'
        };
      }
    } catch (error: any) {
      logger.error('Java security service error during balance check:', {
        error: error.response?.data || error.message,
        walletId,
        status: error.response?.status
      });
      
      // fallback to local wallet service
      if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
        logger.warn('Java service unavailable, falling back to local wallet service');
        return await WalletService.getWalletById(walletId);
      }
      
      return {
        success: false,
        message: 'wallet balance service temporarily unavailable'
      };
    }
  }

  static async validateTransfer(data: {
    senderId: string;
    amount: number;
    currency: Currency;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const response = await axios.post(`${this.JAVA_SECURITY_SERVICE_URL}/wallet/validate-transfer`, {
        senderId: data.senderId,
        amount: data.amount,
        currency: data.currency,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      }, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
          'X-Service-Source': 'typescript-backend'
        }
      });

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          riskScore: response.data.data?.riskScore || 0
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'transfer validation failed',
          riskScore: 100 // high risk if validation fails
        };
      }
    } catch (error: any) {
      logger.error('Java security service error during transfer validation:', {
        error: error.response?.data || error.message,
        senderId: data.senderId,
        amount: data.amount,
        status: error.response?.status
      });
      
      return {
        success: false,
        message: 'transfer validation service temporarily unavailable',
        riskScore: 50 // medium risk if service unavailable
      };
    }
  }

  // health check for java wallet service
  static async healthCheck() {
    try {
      const response = await axios.get(`${this.JAVA_SECURITY_SERVICE_URL}/wallet/health`, {
        timeout: 3000
      });
      
      return {
        success: true,
        status: response.data?.data?.status || 'ok',
        service: 'java-wallet-service'
      };
    } catch (error: any) {
      return {
        success: false,
        status: 'down',
        service: 'java-wallet-service',
        error: error.message
      };
    }
  }
}
