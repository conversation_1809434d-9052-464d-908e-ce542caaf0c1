# AeTrust Platform - User Data Model Documentation

## Overview

The AeTrust platform uses a comprehensive user data model designed for a multi-role fintech platform supporting regular users, agents, merchants, and administrators. The model is built with compliance, security, and scalability in mind, supporting operations in Rwanda and Ethiopia with jurisdiction-specific data handling.

## Core Design Principles

### 1. **Compliance-by-Design**
- Jurisdiction-aware data tagging (RW: Rwanda, ET: Ethiopia)
- GDPR compliance for Rwanda operations
- Data localization support for Ethiopia requirements
- Comprehensive audit trails for regulatory reporting

### 2. **Security-First Architecture**
- Encrypted sensitive data (passwords, PINs, tokens)
- Multi-factor authentication support
- Biometric authentication capabilities
- Account lockout and security monitoring

### 3. **Multi-Role Support**
- Flexible user role system (USER, AGENT, ADMIN, SUPER_ADMIN)
- Role-specific data extensions
- Conditional data requirements based on user type

### 4. **Financial Services Ready**
- Multi-currency wallet support
- Transaction limits and controls
- Commission tracking for agents
- Merchant payment processing capabilities

## Database Tables Structure

### 1. USERS (Core Entity)
**Purpose**: Central user entity containing authentication and basic account information

**Key Fields**:
- `id`: Primary key (bigint)
- `user_uuid`: Unique identifier for external references
- `email`: Unique email address
- `phone`: Unique phone number (international format)
- `password_hash`: Encrypted password
- `user_role`: Enum (USER, AGENT, ADMIN, SUPER_ADMIN)
- `account_status`: Enum (ACTIVE, INACTIVE, SUSPENDED, CLOSED)
- `kyc_status`: Enum (PENDING, UNDER_REVIEW, APPROVED, REJECTED)
- `registration_step`: Current step in registration process

**Indexes**:
- Email, phone, user_uuid (unique)
- Role, status, KYC status (for filtering)
- Created date (for reporting)

### 2. USER_PROFILES
**Purpose**: Personal information and address details

**Key Fields**:
- `user_id`: Foreign key to users table
- `first_name`, `last_name`: Personal names
- `date_of_birth`: Date of birth for age verification
- `profile_picture`: URL to profile image
- Address fields: `street`, `city`, `state`, `country`, `postal_code`

**Compliance Notes**:
- Personal data subject to GDPR (Rwanda) and data protection laws (Ethiopia)
- Address information used for jurisdiction determination

### 3. USER_WALLETS
**Purpose**: Multi-currency wallet management with transaction controls

**Key Fields**:
- `wallet_type`: Enum (MAIN, SAVINGS, BUSINESS, ESCROW)
- `currency`: Currency code (USD, RWF, ETB, etc.)
- `balance`: Total wallet balance
- `available_balance`: Available for transactions
- `pending_balance`: Frozen/pending amounts
- `daily_limit`, `monthly_limit`, `transaction_limit`: Transaction controls

**Financial Controls**:
- Balance tracking with precision (15,2)
- Transaction limits for compliance
- Multi-currency support for cross-border operations

### 4. USER_SECURITY
**Purpose**: Security settings and authentication data

**Key Fields**:
- `transaction_pin_hash`: Encrypted transaction PIN
- `biometric_enabled`: Biometric authentication status
- `two_factor_enabled`: 2FA activation status
- `login_attempts`: Failed login tracking
- `locked_until`: Account lockout expiry
- Verification tokens for password reset and email verification

**Security Features**:
- Account lockout after failed attempts
- Token-based verification system
- Login tracking and monitoring

### 5. USER_PREFERENCES
**Purpose**: User application preferences and notification settings

**Key Fields**:
- `language`: Preferred language (en, fr, rw, am)
- `timezone`: User timezone
- `currency`: Preferred display currency
- Notification preferences for email, SMS, push notifications

### 6. USER_IDENTITY_VERIFICATION (KYC)
**Purpose**: Know Your Customer (KYC) document management

**Key Fields**:
- `id_type`: Enum (NATIONAL_ID, PASSPORT, DRIVERS_LICENSE, VOTERS_CARD)
- `id_number`: ID document number
- Document URLs: `id_document_front`, `id_document_back`, `selfie_photo`
- `verification_status`: Enum (PENDING, UNDER_REVIEW, APPROVED, REJECTED)
- `verifier_id`: Admin who performed verification

**Compliance Features**:
- Document storage with secure URLs
- Verification workflow tracking
- Audit trail for compliance reporting

### 7. USER_AGENT_INFO
**Purpose**: Agent-specific business information and performance tracking

**Key Fields**:
- `commission_rate`: Agent commission percentage
- `total_commission_earned`: Lifetime commission earnings
- Business information: `business_name`, `business_type`, `business_registration_number`
- `performance_rating`: Performance score (1-5)
- `monthly_commission`: Current month earnings

**Agent Features**:
- Commission tracking and calculation
- Performance monitoring
- Business document management

### 8. USER_MERCHANT_INFO
**Purpose**: Merchant-specific information for payment processing

**Key Fields**:
- `api_key`: Unique API key for merchant integration
- `webhook_url`: Callback URL for payment notifications
- `merchant_category`: Business category code
- `settlement_account`: Account for payment settlements

**Merchant Features**:
- API key management
- Webhook configuration
- Settlement account tracking

### 9. AGENTS (Separate Entity)
**Purpose**: Detailed agent location and service information

**Key Fields**:
- `agent_code`: Unique agent identifier
- Location data: `latitude`, `longitude`, address fields
- `operating_hours`: JSON string with operating schedule
- `services`: JSON string with available services
- `cash_balance`, `float_balance`: Agent liquidity tracking

**Agent Network Features**:
- Geographic location tracking
- Service availability management
- Cash flow monitoring

### 10. SYSTEM_CONFIGS
**Purpose**: Platform configuration and feature flags

**Key Fields**:
- `config_key`: Unique configuration identifier
- `config_value`: Configuration value (supports multiple types)
- `config_type`: Value type (string, int, bool, json)
- `category`: Configuration grouping

## Data Relationships

### Primary Relationships
- **Users → User_Profiles**: 1:1 (Optional)
- **Users → User_Wallets**: 1:N (Multiple wallets per user)
- **Users → User_Security**: 1:1 (Required)
- **Users → User_Preferences**: 1:1 (Optional, defaults applied)
- **Users → User_Identity_Verification**: 1:1 (Required for full access)

### Role-Specific Relationships
- **Users → User_Agent_Info**: 1:1 (Only for AGENT role)
- **Users → User_Merchant_Info**: 1:1 (Only for MERCHANT role)
- **Users → Agents**: 1:1 (Extended agent information)

### Transaction Relationships
- **User_Wallets → Transactions**: 1:N (Transaction history)
- **Users → Audit_Logs**: 1:N (Compliance tracking)

## Compliance and Security Features

### Data Protection (GDPR - Rwanda)
- **Consent Management**: Explicit consent tracking for data processing
- **Right to Access**: Automated data export capabilities
- **Right to Erasure**: Soft delete with data anonymization
- **Data Portability**: Standardized export formats
- **Breach Notification**: Automated compliance reporting

### Data Localization (Ethiopia)
- **Jurisdiction Tagging**: All records tagged with jurisdiction (ET/RW)
- **Data Residency**: Automatic routing to appropriate data centers
- **Cross-Border Controls**: Restrictions on data movement
- **Local Processing**: Ensure processing within Ethiopian borders

### Financial Compliance
- **AML/CFT**: Transaction monitoring and suspicious activity detection
- **KYC Requirements**: Document verification and identity validation
- **Transaction Limits**: Configurable limits based on verification level
- **Audit Trails**: Immutable transaction and action logging

## Indexing Strategy

### Performance Indexes
- **Users**: email, phone, user_uuid (unique lookups)
- **User_Wallets**: user_id, currency, wallet_type (wallet operations)
- **Transactions**: user_id, wallet_id, created_at (transaction history)

### Compliance Indexes
- **Users**: kyc_status, account_status (compliance filtering)
- **Audit_Logs**: user_id, action, created_at (audit queries)
- **User_Identity_Verification**: verification_status (KYC reporting)

### Reporting Indexes
- **Users**: created_at, role (user analytics)
- **User_Agent_Info**: is_active, verification_status (agent reporting)
- **Transactions**: created_at, transaction_type, status (financial reporting)

## Data Retention and Archival

### Retention Policies
- **Transaction Data**: 7 years (regulatory requirement)
- **KYC Documents**: 5 years after account closure
- **Audit Logs**: 10 years (compliance requirement)
- **User Profiles**: Until account deletion request

### Archival Strategy
- **Hot Data**: Last 2 years (high-performance storage)
- **Warm Data**: 2-5 years (standard storage)
- **Cold Data**: 5+ years (archival storage)

## Security Considerations

### Encryption
- **At Rest**: AES-256 encryption for sensitive fields
- **In Transit**: TLS 1.3 for all data transmission
- **Field Level**: Additional encryption for PII and financial data

### Access Controls
- **Role-Based Access**: Granular permissions based on user roles
- **Data Masking**: PII masking for non-authorized users
- **Audit Logging**: All data access logged for compliance

### Backup and Recovery
- **Real-time Replication**: Critical data replicated across regions
- **Daily Backups**: Encrypted backups to secure storage
- **Point-in-Time Recovery**: Transaction-level recovery capabilities

This data model provides a robust foundation for the AeTrust platform, ensuring compliance, security, and scalability while supporting the diverse needs of users, agents, merchants, and administrators across Rwanda and Ethiopia.
