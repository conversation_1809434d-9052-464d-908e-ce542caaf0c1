// import { setupTestDb, teardownTestDb, clearDatabase, createTestUser } from '../setup';
// import { RegistrationService } from '../../src/services/registration.service';
// import { AuthService } from '../../src/services/auth.service';
// import { UserService } from '../../src/services/user.service';
// import { User } from '../../src/models/user.model';
// import bcrypt from 'bcrypt';

// describe('User Journey Unit Tests', () => {
//   beforeAll(async () => {
//     await setupTestDb();
//   }, 30000);

//   afterAll(async () => {
//     await teardownTestDb();
//   }, 30000);

//   beforeEach(async () => {
//     await clearDatabase();
//   });

//   describe('Complete User Registration and Login Flow', () => {
//     it('should complete full user registration and login journey', async () => {
//       const timestamp = Date.now();
//       const userEmail = `unit-test-user-${timestamp}@example.com`;
//       const userPhone = `+234812345${timestamp.toString().slice(-4)}`;
//       const userPassword = 'Test123!@#';

//       // Step 1: Initiate registration
//       const initiateResult = await RegistrationService.initiateRegistration({
//         email: userEmail,
//         phone: userPhone,
//         password: userPassword,
//         confirmPassword: userPassword,
//         acceptTerms: true
//       });

//       expect(initiateResult.success).toBe(true);
//       expect(initiateResult.data).toHaveProperty('userId');
//       expect(initiateResult.data).toHaveProperty('nextStep');

//       const userId = initiateResult.data!.userId;

//       // Step 2: Verify phone number
//       const verifyPhoneResult = await RegistrationService.verifyPhone({
//         userId,
//         verificationCode: '123456'
//       });

//       expect(verifyPhoneResult.success).toBe(true);

//       // Step 3: Verify email
//       const verifyEmailResult = await RegistrationService.verifyEmail({
//         userId,
//         verificationCode: '123456'
//       });

//       expect(verifyEmailResult.success).toBe(true);

//       // Step 4: Complete profile
//       const completeProfileResult = await RegistrationService.completeProfile({
//         userId,
//         firstName: 'John',
//         lastName: 'Doe',
//         dateOfBirth: '1990-01-01',
//         address: {
//           street: '123 Test Street',
//           city: 'Lagos',
//           state: 'Lagos',
//           country: 'Nigeria',
//           postalCode: '100001'
//         }
//       });

//       expect(completeProfileResult.success).toBe(true);

//       // Step 5: Set transaction PIN
//       const setPinResult = await RegistrationService.setTransactionPin({
//         userId,
//         pin: '1234',
//         confirmPin: '1234'
//       });

//       expect(setPinResult.success).toBe(true);

//       // Step 6: Complete registration
//       const completeResult = await RegistrationService.completeRegistration({
//         userId
//       });

//       expect(completeResult.success).toBe(true);
//       expect(completeResult.data).toHaveProperty('token');

//       // Step 7: Login with new credentials
//       const loginResult = await AuthService.login({
//         email: userEmail,
//         password: userPassword
//       });

//       expect(loginResult.success).toBe(true);
//       if (loginResult.success && loginResult.data) {
//         expect(loginResult.data).toHaveProperty('token');
//         expect(loginResult.data.user.email).toBe(userEmail);
//       }

//       // Step 8: Get user profile
//       const user = await UserService.getUserByEmail(userEmail);
//       expect(user).toBeTruthy();
//       if (user) {
//         expect(user.email).toBe(userEmail);
//         expect(user.first_name).toBe('John');
//         expect(user.last_name).toBe('Doe');
//       }

//       // Cleanup
//       await User.deleteOne({ email: userEmail });
//     });

//     it('should handle registration validation errors', async () => {
//       // Test with invalid email
//       const invalidEmailResult = await RegistrationService.initiateRegistration({
//         email: 'invalid-email',
//         phone: '+2348123456789',
//         password: 'Test123!@#',
//         confirmPassword: 'Test123!@#',
//         acceptTerms: true
//       });

//       expect(invalidEmailResult.success).toBe(false);

//       // Test with weak password
//       const weakPasswordResult = await RegistrationService.initiateRegistration({
//         email: '<EMAIL>',
//         phone: '+2348123456789',
//         password: '123',
//         confirmPassword: '123',
//         acceptTerms: true
//       });

//       expect(weakPasswordResult.success).toBe(false);

//       // Test with mismatched passwords
//       const mismatchedPasswordResult = await RegistrationService.initiateRegistration({
//         email: '<EMAIL>',
//         phone: '+2348123456789',
//         password: 'Test123!@#',
//         confirmPassword: 'Different123!@#',
//         acceptTerms: true
//       });

//       expect(mismatchedPasswordResult.success).toBe(false);
//     });
//   });

//   describe('User Profile Management Journey', () => {
//     let userId: string;
//     let userEmail: string;

//     beforeEach(async () => {
//       // Create a test user for profile tests
//       userEmail = `profile-${Date.now()}@example.com`;
//       const userData = createTestUser({
//         email: userEmail,
//         password: await bcrypt.hash('Test123!@#', 10)
//       });
      
//       const user = new User(userData);
//       await user.save();
//       userId = user._id.toString();
//     });

//     afterEach(async () => {
//       // Cleanup test user
//       if (userId) {
//         await User.findByIdAndDelete(userId);
//       }
//     });

//     it('should update user profile successfully', async () => {
//       const updateResult = await UserService.updateProfile(userId, {
//         firstName: 'Updated',
//         lastName: 'Name',
//         bio: 'Updated bio description'
//       });

//       expect(updateResult).toBeTruthy();
//       if (updateResult) {
//         expect(updateResult.success).toBe(true);
//         if (updateResult.success && updateResult.data) {
//           expect(updateResult.data.first_name).toBe('Updated');
//           expect(updateResult.data.last_name).toBe('Name');
//           expect(updateResult.data.bio).toBe('Updated bio description');
//         }
//       }
//     });

//     it('should change password successfully', async () => {
//       const changePasswordResult = await AuthService.changePassword({
//         userId,
//         currentPassword: 'Test123!@#',
//         newPassword: 'NewPassword123!@#',
//         confirmPassword: 'NewPassword123!@#'
//       });

//       expect(changePasswordResult.success).toBe(true);

//       // Verify new password works by attempting login
//       const loginResult = await AuthService.login({
//         email: userEmail,
//         password: 'NewPassword123!@#'
//       });

//       expect(loginResult.success).toBe(true);
//       if (loginResult.success && loginResult.data) {
//         expect(loginResult.data).toHaveProperty('token');
//       }
//     });

//     it('should get user statistics', async () => {
//       const statsResult = await UserService.getUserStats(userId);

//       expect(statsResult.success).toBe(true);
//       if (statsResult.success && statsResult.data) {
//         expect(statsResult.data).toHaveProperty('totalTransactions');
//         expect(statsResult.data).toHaveProperty('totalVolume');
//         expect(statsResult.data).toHaveProperty('walletBalance');
//         expect(statsResult.data).toHaveProperty('accountAge');
//       }
//     });

//     it('should update user status', async () => {
//       const updateStatusResult = await UserService.updateUserStatus(userId, 'suspended' as any);

//       expect(updateStatusResult.success).toBe(true);
//       if (updateStatusResult.success && updateStatusResult.data) {
//         expect(updateStatusResult.data.account_status).toBe('suspended');
//       }
//     });

//     it('should delete user account', async () => {
//       const deleteResult = await UserService.deleteAccount(userId);

//       expect(deleteResult.success).toBe(true);
//       if (deleteResult.success) {
//         expect(deleteResult.message).toContain('account deleted successfully');
//       }

//       // Verify user is soft deleted
//       const deletedUser = await User.findById(userId);
//       expect(deletedUser?.deleted_at).toBeTruthy();
//     });
//   });

//   describe('Authentication Journey', () => {
//     let userEmail: string;
//     let userId: string;

//     beforeEach(async () => {
//       userEmail = `auth-${Date.now()}@example.com`;
//       const userData = createTestUser({
//         email: userEmail,
//         password: await bcrypt.hash('Test123!@#', 10)
//       });
      
//       const user = new User(userData);
//       await user.save();
//       userId = user._id.toString();
//     });

//     afterEach(async () => {
//       if (userId) {
//         await User.findByIdAndDelete(userId);
//       }
//     });

//     it('should complete full authentication flow', async () => {
//       // Login
//       const loginResult = await AuthService.login({
//         email: userEmail,
//         password: 'Test123!@#'
//       });

//       expect(loginResult.success).toBe(true);
//       if (!loginResult.success || !loginResult.data) {
//         throw new Error('Login failed');
//       }

//       const token = loginResult.data.token;

//       // Validate token
//       const validateResult = await AuthService.validateToken(token);
//       expect(validateResult.success).toBe(true);

//       // Refresh token
//       const refreshResult = await AuthService.refreshToken(token);
//       expect(refreshResult.success).toBe(true);
//       if (refreshResult.success && refreshResult.data && 'token' in refreshResult.data) {
//         expect(refreshResult.data.token).not.toBe(token);
//       }

//       // Logout
//       const logoutResult = await AuthService.logout(token);
//       expect(logoutResult.success).toBe(true);
//     });

//     it('should handle password reset flow', async () => {
//       // Initiate password reset
//       const resetResult = await AuthService.resetPassword({
//         email: userEmail
//       });

//       expect(resetResult.success).toBe(true);
//       if (resetResult.success) {
//         expect(resetResult.message).toContain('password reset');
//       }
//     });
//   });
// });
