package com.aetrust.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.Set;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;


@Slf4j
@Service
public class CacheService {
    
    @Value("${spring.redis.enabled:false}")
    private boolean redisEnabled;
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private InMemorySessionService inMemoryService;
    
    private boolean isRedisAvailable = false;
    
    public CacheService() {
        log.info("CacheService initialized - will auto-detect Redis availability");
    }
    
   
    private boolean checkRedisAvailability() {
        if (!redisEnabled || redisTemplate == null) {
            return false;
        }
        
        try {
            // ping test
            redisTemplate.opsForValue().set("health_check", "ok", 1, TimeUnit.SECONDS);
            String result = (String) redisTemplate.opsForValue().get("health_check");
            isRedisAvailable = "ok".equals(result);
            
            if (isRedisAvailable) {
                log.debug("Redis is available and working");
            }
            
            return isRedisAvailable;
        } catch (Exception e) {
            log.warn("Redis not available: {}", e.getMessage());
            isRedisAvailable = false;
            return false;
        }
    }
    
   
    public void set(String key, String value, long timeout, TimeUnit unit) {
        try {
            if (checkRedisAvailability()) {
                redisTemplate.opsForValue().set(key, value, timeout, unit);
                log.debug("Cached in Redis: {}", key);
            } else {
                inMemoryService.set(key, value, timeout, unit);
                log.debug("Cached in memory: {}", key);
            }
        } catch (Exception e) {
            log.error("Cache set error for key {}: {}", key, e.getMessage());
            // Fallback to in-memory
            inMemoryService.set(key, value, timeout, unit);
        }
    }
    
  
    public void set(String key, String value) {
        set(key, value, -1, TimeUnit.SECONDS);
    }
    
   
    public String get(String key) {
        try {
            if (checkRedisAvailability()) {
                Object value = redisTemplate.opsForValue().get(key);
                return value != null ? value.toString() : null;
            } else {
                return inMemoryService.get(key);
            }
        } catch (Exception e) {
            log.error("Cache get error for key {}: {}", key, e.getMessage());
            // Fallback to in-memory
            return inMemoryService.get(key);
        }
    }
    
  
    public void delete(String key) {
        try {
            if (checkRedisAvailability()) {
                redisTemplate.delete(key);
                log.debug("Deleted from Redis: {}", key);
            } else {
                inMemoryService.delete(key);
                log.debug("Deleted from memory: {}", key);
            }
        } catch (Exception e) {
            log.error("Cache delete error for key {}: {}", key, e.getMessage());
            // Fallback to in-memory
            inMemoryService.delete(key);
        }
    }
    
  
    public boolean exists(String key) {
        try {
            if (checkRedisAvailability()) {
                return Boolean.TRUE.equals(redisTemplate.hasKey(key));
            } else {
                return inMemoryService.exists(key);
            }
        } catch (Exception e) {
            log.error("Cache exists error for key {}: {}", key, e.getMessage());
            // Fallback to in-memory
            return inMemoryService.exists(key);
        }
    }
    
  
    public Set<String> keys(String pattern) {
        try {
            if (checkRedisAvailability()) {
                return redisTemplate.keys(pattern);
            } else {
                return inMemoryService.keys(pattern);
            }
        } catch (Exception e) {
            log.error("Cache keys error for pattern {}: {}", pattern, e.getMessage());
            // Fallback to in-memory
            return inMemoryService.keys(pattern);
        }
    }
    
  
    public void expire(String key, long timeout, TimeUnit unit) {
        try {
            if (checkRedisAvailability()) {
                redisTemplate.expire(key, timeout, unit);
            } else {
                // For in-memory, we need to get and re-set with expiration
                String value = inMemoryService.get(key);
                if (value != null) {
                    inMemoryService.set(key, value, timeout, unit);
                }
            }
        } catch (Exception e) {
            log.error("Cache expire error for key {}: {}", key, e.getMessage());
        }
    }
    
   
    public Long increment(String key) {
        try {
            if (checkRedisAvailability()) {
                return redisTemplate.opsForValue().increment(key);
            } else {
                String current = inMemoryService.get(key);
                long value = current != null ? Long.parseLong(current) + 1 : 1;
                inMemoryService.set(key, String.valueOf(value), 24, TimeUnit.HOURS);
                return value;
            }
        } catch (Exception e) {
            log.error("Cache increment error for key {}: {}", key, e.getMessage());
            return 1L;
        }
    }
    
   
    public void clear() {
        try {
            if (checkRedisAvailability()) {
                Set<String> keys = redisTemplate.keys("*");
                if (keys != null && !keys.isEmpty()) {
                    redisTemplate.delete(keys);
                }
                log.info("Redis cache cleared");
            } else {
                inMemoryService.clear();
                log.info("In-memory cache cleared");
            }
        } catch (Exception e) {
            log.error("Cache clear error: {}", e.getMessage());
        }
    }
    
   
    public Map<String, Object> getCacheInfo() {
        Map<String, Object> info = new ConcurrentHashMap<>();
        info.put("redis_enabled", redisEnabled);
        info.put("redis_available", isRedisAvailable);
        info.put("current_backend", isRedisAvailable ? "Redis" : "In-Memory");
        
        try {
            if (checkRedisAvailability()) {
                Set<String> keys = redisTemplate.keys("*");
                info.put("total_keys", keys != null ? keys.size() : 0);
            } else {
                info.put("total_keys", inMemoryService.size());
            }
        } catch (Exception e) {
            info.put("total_keys", "unknown");
        }
        
        return info;
    }
    
 
    public boolean isHealthy() {
        try {
            String testKey = "health_test_" + System.currentTimeMillis();
            set(testKey, "test", 5, TimeUnit.SECONDS);
            String result = get(testKey);
            delete(testKey);
            return "test".equals(result);
        } catch (Exception e) {
            log.error("Cache health check failed: {}", e.getMessage());
            return false;
        }
    }
}
