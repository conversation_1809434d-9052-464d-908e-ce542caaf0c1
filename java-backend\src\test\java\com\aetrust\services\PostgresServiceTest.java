package com.aetrust.services;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
public class PostgresServiceTest {
    
    @Autowired
    private PostgresService postgresService;
    
    @Autowired
    private DataSource dataSource;
    
    @Test
    public void testDatabaseConnection() throws SQLException {
        // test basic connection
        try (Connection conn = dataSource.getConnection()) {
            assertNotNull(conn);
            assertFalse(conn.isClosed());
            assertTrue(conn.isValid(5));
        }
    }
    
    @Test
    public void testGetConnectionInfo() throws SQLException {
        Map<String, Object> info = postgresService.getConnectionInfo();
        
        assertNotNull(info);
        assertTrue(info.containsKey("database_product_name"));
        assertTrue(info.containsKey("driver_name"));
        assertTrue(info.containsKey("url"));
        
        // should be postgresql
        String productName = (String) info.get("database_product_name");
        assertTrue(productName.toLowerCase().contains("postgresql"));
    }
    
    @Test
    public void testExecuteCustomQuery() throws SQLException {
        // test simple query
        var results = postgresService.executeCustomQuery("SELECT 1 as test_value, 'hello' as test_string");
        
        assertNotNull(results);
        assertEquals(1, results.size());
        
        Map<String, Object> row = results.get(0);
        assertEquals(1, row.get("test_value"));
        assertEquals("hello", row.get("test_string"));
    }
    
    @Test
    public void testExecuteCustomQueryWithParams() throws SQLException {
        // test query with parameters
        var results = postgresService.executeCustomQuery(
            "SELECT ? as param_value, ? as param_string", 
            42, "test"
        );
        
        assertNotNull(results);
        assertEquals(1, results.size());
        
        Map<String, Object> row = results.get(0);
        assertEquals(42, row.get("param_value"));
        assertEquals("test", row.get("param_string"));
    }
}
