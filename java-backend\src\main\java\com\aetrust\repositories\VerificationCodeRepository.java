package com.aetrust.repositories;

import com.aetrust.models.VerificationCode;
import com.aetrust.models.VerificationCode.VerificationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface VerificationCodeRepository extends JpaRepository<VerificationCode, Long> {
    
    // Find active verification code for user
    @Query("SELECT vc FROM VerificationCode vc WHERE vc.userIdentifier = :userIdentifier " +
           "AND vc.verificationType = :type AND vc.isUsed = false AND vc.expiresAt > :now " +
           "ORDER BY vc.createdAt DESC")
    Optional<VerificationCode> findActiveCode(@Param("userIdentifier") String userIdentifier, 
                                            @Param("type") VerificationType type, 
                                            @Param("now") LocalDateTime now);
    
    // Find latest verification code for user (regardless of status)
    @Query("SELECT vc FROM VerificationCode vc WHERE vc.userIdentifier = :userIdentifier " +
           "AND vc.verificationType = :type ORDER BY vc.createdAt DESC")
    Optional<VerificationCode> findLatestCode(@Param("userIdentifier") String userIdentifier, 
                                            @Param("type") VerificationType type);
    
    // Find all verification codes for a user
    List<VerificationCode> findByUserIdentifierOrderByCreatedAtDesc(String userIdentifier);
    
    // Find all verification codes for a user by type
    List<VerificationCode> findByUserIdentifierAndVerificationTypeOrderByCreatedAtDesc(
        String userIdentifier, VerificationType type);
    
    // Find expired codes
    List<VerificationCode> findByExpiresAtBefore(LocalDateTime dateTime);
    
    // Find unused codes that are expired
    @Query("SELECT vc FROM VerificationCode vc WHERE vc.isUsed = false AND vc.expiresAt < :now")
    List<VerificationCode> findExpiredUnusedCodes(@Param("now") LocalDateTime now);
    
    // Count verification attempts in time window
    @Query("SELECT COUNT(vc) FROM VerificationCode vc WHERE vc.userIdentifier = :userIdentifier " +
           "AND vc.verificationType = :type AND vc.createdAt > :since")
    long countRecentAttempts(@Param("userIdentifier") String userIdentifier, 
                           @Param("type") VerificationType type, 
                           @Param("since") LocalDateTime since);
    
    // Delete expired codes
    @Modifying
    @Query("DELETE FROM VerificationCode vc WHERE vc.expiresAt < :now")
    int deleteExpiredCodes(@Param("now") LocalDateTime now);
    
    // Delete used codes older than specified time
    @Modifying
    @Query("DELETE FROM VerificationCode vc WHERE vc.isUsed = true AND vc.usedAt < :cutoff")
    int deleteOldUsedCodes(@Param("cutoff") LocalDateTime cutoff);
    
    // Delete all codes for a user (for cleanup after successful registration)
    @Modifying
    @Query("DELETE FROM VerificationCode vc WHERE vc.userIdentifier = :userIdentifier")
    int deleteByUserIdentifier(@Param("userIdentifier") String userIdentifier);

    // Invalidate existing codes for a user and type
    @Modifying
    @Query("UPDATE VerificationCode vc SET vc.isUsed = true, vc.usedAt = :usedAt " +
           "WHERE vc.userIdentifier = :userIdentifier AND vc.verificationType = :type AND vc.isUsed = false")
    int invalidateExistingCodes(@Param("userIdentifier") String userIdentifier,
                               @Param("type") VerificationType type,
                               @Param("usedAt") LocalDateTime usedAt);

    // Check if verification code exists and is valid
    boolean existsByUserIdentifierAndCodeAndVerificationTypeAndIsUsedFalseAndExpiresAtAfter(
        String userIdentifier, String code, VerificationType type, LocalDateTime now);
}
