server:
  port: ${PORT:3000}
  servlet:
    context-path: /api/v1

spring:
  application:
    name: aetrust-backend
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  
  datasource:
    url: ${DATABASE_URL:********************************************}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:6993954386}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DB_CONNECTION_POOL_SIZE:20}
      minimum-idle: ${DB_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}
      leak-detection-threshold: ${DB_LEAK_DETECTION:60000}
      validation-timeout: ${DB_VALIDATION_TIMEOUT:5000}
      initialization-fail-timeout: ${DB_INIT_FAIL_TIMEOUT:1}
      connection-test-query: SELECT 1
      auto-commit: false
      # additional postgresql optimizations
      data-source-properties:
        cachePrepStmts: ${DB_CACHE_PREP_STMTS:true}
        prepStmtCacheSize: ${DB_PREP_STMT_CACHE_SIZE:250}
        prepStmtCacheSqlLimit: ${DB_PREP_STMT_CACHE_SQL_LIMIT:2048}
        useServerPrepStmts: ${DB_USE_SERVER_PREP_STMTS:true}
        useLocalSessionState: ${DB_USE_LOCAL_SESSION_STATE:true}
        rewriteBatchedStatements: ${DB_REWRITE_BATCHED_STATEMENTS:true}
        cacheResultSetMetadata: ${DB_CACHE_RESULT_SET_METADATA:true}
        cacheServerConfiguration: ${DB_CACHE_SERVER_CONFIG:true}
        elideSetAutoCommits: ${DB_ELIDE_SET_AUTO_COMMITS:true}
        maintainTimeStats: ${DB_MAINTAIN_TIME_STATS:false}
        # postgresql specific settings
        ApplicationName: ${DB_APPLICATION_NAME:aetrust-backend}
        connectTimeout: ${DB_CONNECT_TIMEOUT:10}
        socketTimeout: ${DB_SOCKET_TIMEOUT:30}
        tcpKeepAlive: ${DB_TCP_KEEP_ALIVE:true}
        logUnclosedConnections: ${DB_LOG_UNCLOSED_CONNECTIONS:false}

  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:update}
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    properties:
      hibernate:
        format_sql: ${FORMAT_SQL:false}
        show_sql: ${SHOW_SQL:false}
        use_sql_comments: ${SQL_COMMENTS:false}
        transaction:
          jta:
            platform: none
        jdbc:
          time_zone: UTC
          batch_size: ${BATCH_SIZE:25}
          fetch_size: ${FETCH_SIZE:50}
        connection:
          provider_disables_autocommit: true
        cache:
          use_second_level_cache: ${USE_L2_CACHE:false}
          use_query_cache: ${USE_QUERY_CACHE:false}
          # region:
          #   factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        generate_statistics: ${HIBERNATE_STATS:false}
      
  # Redis Configuration
  redis:
    enabled: ${REDIS_ENABLED:false}
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}
    timeout: ${REDIS_CONNECT_TIMEOUT:10000}ms
    lettuce:
      pool:
        max-active: ${REDIS_POOL_MAX_ACTIVE:10}
        max-idle: ${REDIS_POOL_MAX_IDLE:10}
        min-idle: ${REDIS_POOL_MIN_IDLE:1}
      shutdown-timeout: ${REDIS_COMMAND_TIMEOUT:5000}ms
        
  security:
    jwt:
      secret: ${JWT_SECRET:super-secret-jwt-key}
      refresh-secret: ${JWT_REFRESH_SECRET:super-secret-refresh-key}
      expiration: ${JWT_EXPIRATION:86400000}
      refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000}
      expires-in: ${JWT_EXPIRES_IN:15m}
      refresh-expires-in: ${JWT_REFRESH_EXPIRES_IN:7d}
      
  jackson:
    default-property-inclusion: NON_NULL
    
logging:
  level:
    com.aetrust: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
    org.springframework.transaction: ${TX_LOG_LEVEL:WARN}
    com.zaxxer.hikari: ${HIKARI_LOG_LEVEL:WARN}
    io.github.resilience4j: ${RESILIENCE_LOG_LEVEL:INFO}
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr(%m){green}%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/aetrust-backend.log
    max-size: ${LOG_MAX_SIZE:100MB}
    max-history: ${LOG_MAX_HISTORY:30}
    total-size-cap: ${LOG_TOTAL_SIZE:1GB}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,circuitbreakers,ratelimiters
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
      probes:
        enabled: true
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
      environment: ${ENVIRONMENT:development}

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    instances:
      userService:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
      authService:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 60
        wait-duration-in-open-state: 30s
  retry:
    instances:
      userService:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
      authService:
        max-attempts: 3
        wait-duration: 1s
  ratelimiter:
    instances:
      userService:
        limit-for-period: 100
        limit-refresh-period: 1s
        timeout-duration: 0s
      authService:
        limit-for-period: 50
        limit-refresh-period: 1s
        timeout-duration: 0s

aetrust:
  security:
    salt-rounds: ${BCRYPT_SALT_ROUNDS:12}
    encryption-key: ${ENCRYPTION_KEY:your-encryption-key-32-chars-long}
    max-login-attempts: ${MAX_LOGIN_ATTEMPTS:5}
    lockout-duration: ${LOCKOUT_DURATION:900000}
    rate-limit:
      ip-limit: ${IP_RATE_LIMIT:100}
      user-limit: ${USER_RATE_LIMIT:50}
      window: ${RATE_LIMIT_WINDOW:3600000}
    verification:
      max-attempts: ${VERIFICATION_MAX_ATTEMPTS:5}
      code-expiry-minutes: ${VERIFICATION_CODE_EXPIRY:15}
      rate-limit-window-hours: ${VERIFICATION_RATE_LIMIT_WINDOW:1}
    session:
      timeout-minutes: ${SESSION_TIMEOUT:30}
      cleanup-interval-hours: ${SESSION_CLEANUP_INTERVAL:24}
  
  fraud:
    velocity-threshold: ${FRAUD_VELOCITY_THRESHOLD:5}
    amount-threshold: ${FRAUD_AMOUNT_THRESHOLD:10000}
    location-check-enabled: ${FRAUD_LOCATION_CHECK:true}
    
  file:
    upload:
      max-size: ${MAX_FILE_SIZE:10MB}
      allowed-types: jpg,jpeg,png,pdf
      directory: ${UPLOAD_DIRECTORY:uploads}
      kyc-max-size: ${KYC_MAX_FILE_SIZE:20MB}
      virus-scan-enabled: ${VIRUS_SCAN_ENABLED:false}

  cdn:
    base-url: ${CDN_BASE_URL:https://cdn.aetrust.com}

# Cloudinary Configuration
cloudinary:
  enabled: ${CLOUDINARY_ENABLED:false}
  cloud-name: ${CLOUDINARY_CLOUD_NAME:}
  api-key: ${CLOUDINARY_API_KEY:}
  api-secret: ${CLOUDINARY_API_SECRET:}
  folder: ${CLOUDINARY_FOLDER:aetrust}
      
  notification:
    sms:
      provider: ${SMS_PROVIDER:twilio}
      api-key: ${SMS_API_KEY:}
    email:
      provider: ${EMAIL_PROVIDER:sendgrid}
      api-key: ${EMAIL_API_KEY:}

  # Feature Flags for Security and Functionality
  feature-flags:
    threat-intelligence:
      enabled: ${FEATURE_THREAT_INTELLIGENCE:true}
    device-fingerprinting:
      enabled: ${FEATURE_DEVICE_FINGERPRINTING:true}
    geo-blocking:
      enabled: ${FEATURE_GEO_BLOCKING:true}
    behavioral-analysis:
      enabled: ${FEATURE_BEHAVIORAL_ANALYSIS:true}
    advanced-logging:
      enabled: ${FEATURE_ADVANCED_LOGGING:true}
    circuit-breaker:
      enabled: ${FEATURE_CIRCUIT_BREAKER:true}
    rate-limiting:
      enabled: ${FEATURE_RATE_LIMITING:true}
    brute-force-protection:
      enabled: ${FEATURE_BRUTE_FORCE_PROTECTION:true}
    sql-injection-protection:
      enabled: ${FEATURE_SQL_INJECTION_PROTECTION:true}
    xss-protection:
      enabled: ${FEATURE_XSS_PROTECTION:true}
    csrf-protection:
      enabled: ${FEATURE_CSRF_PROTECTION:true}
    input-validation:
      enabled: ${FEATURE_INPUT_VALIDATION:true}
    output-encoding:
      enabled: ${FEATURE_OUTPUT_ENCODING:true}
    secure-headers:
      enabled: ${FEATURE_SECURE_HEADERS:true}
