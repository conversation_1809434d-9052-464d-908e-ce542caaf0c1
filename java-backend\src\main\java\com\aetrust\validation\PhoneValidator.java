package com.aetrust.validation;

import com.aetrust.services.ValidationService;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Autowired;

public class PhoneValidator implements ConstraintValidator<ValidPhone, String> {
    
    @Autowired
    private ValidationService validationService;
    
    @Override
    public void initialize(ValidPhone constraintAnnotation) {
        // No initialization needed
    }
    
    @Override
    public boolean isValid(String phone, ConstraintValidatorContext context) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        
        return validationService != null && validationService.isValidPhone(phone);
    }
}
