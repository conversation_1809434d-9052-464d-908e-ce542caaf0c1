package com.aetrust.dto;

import com.aetrust.validation.Validation.ValidPhone;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.*;
import jakarta.validation.Valid;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentRegistrationRequest {
    
    @NotBlank(message = "business name is required")
    @Size(min = 2, max = 200, message = "business name must be between 2 and 200 characters")
    private String businessName;
    
    @NotBlank(message = "business type is required")
    @Pattern(regexp = "^(retail_shop|mobile_money_agent|bank_agent|pharmacy|supermarket|other)$", 
             message = "invalid business type")
    private String businessType;
    
    @Size(max = 50, message = "business registration number too long")
    private String businessRegistrationNumber;
    
    @Size(max = 50, message = "tax ID too long")
    private String taxId;
    
    @NotNull(message = "location is required")
    @Valid
    private LocationDto location;
    
    @NotNull(message = "contact info is required")
    @Valid
    private ContactInfoDto contactInfo;
    
    @Valid
    private OperatingHoursDto operatingHours;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationDto {
        
        @NotBlank(message = "address is required")
        @Size(max = 500, message = "address too long")
        private String address;
        
        @NotBlank(message = "city is required")
        @Size(max = 100, message = "city name too long")
        private String city;
        
        @NotBlank(message = "state is required")
        @Size(max = 100, message = "state name too long")
        private String state;
        
        @NotBlank(message = "country is required")
        @Size(max = 100, message = "country name too long")
        private String country;
        
        @Size(max = 20, message = "postal code too long")
        private String postalCode;
        
        @Valid
        private CoordinatesDto coordinates;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CoordinatesDto {
        
        @NotNull(message = "latitude is required")
        @DecimalMin(value = "-90.0", message = "latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "latitude must be between -90 and 90")
        private Double latitude;
        
        @NotNull(message = "longitude is required")
        @DecimalMin(value = "-180.0", message = "longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "longitude must be between -180 and 180")
        private Double longitude;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfoDto {
        
        @NotBlank(message = "phone number is required")
        @ValidPhone(message = "phone number must be in international format")
        private String phone;
        
        @NotBlank(message = "email is required")
        @Email(message = "invalid email format")
        private String email;
        
        @ValidPhone(message = "whatsapp number must be in international format")
        private String whatsapp;
        
        @ValidPhone(message = "alternate phone must be in international format")
        private String alternatePhone;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperatingHoursDto {
        
        @Min(value = 0, message = "start hour must be between 0 and 23")
        @Max(value = 23, message = "start hour must be between 0 and 23")
        private Integer startHour = 8;
        
        @Min(value = 0, message = "end hour must be between 0 and 23")
        @Max(value = 23, message = "end hour must be between 0 and 23")
        private Integer endHour = 20;
        
        private boolean isOpen24Hours = false;
    }
}
