package com.aetrust.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
@Validated
public class DatabaseConfig {
    
    @NotBlank(message = "Database URL is required")
    private String url;
    
    @NotBlank(message = "Database username is required")
    private String username;
    
    @NotBlank(message = "Database password is required")
    private String password;
    
    @NotBlank(message = "Database driver class name is required")
    private String driverClassName = "org.postgresql.Driver";
    
    private HikariProperties hikari = new HikariProperties();
    
    @Data
    public static class HikariProperties {
        
        @Min(value = 1, message = "Maximum pool size must be at least 1")
        private int maximumPoolSize = 20;
        
        @Min(value = 0, message = "Minimum idle must be at least 0")
        private int minimumIdle = 5;
        
        @Min(value = 1000, message = "Connection timeout must be at least 1000ms")
        private long connectionTimeout = 30000;
        
        @Min(value = 1000, message = "Idle timeout must be at least 1000ms")
        private long idleTimeout = 600000;
        
        @Min(value = 1000, message = "Max lifetime must be at least 1000ms")
        private long maxLifetime = 1800000;
        
        @Min(value = 0, message = "Leak detection threshold must be at least 0")
        private long leakDetectionThreshold = 60000;
        
        @Min(value = 1000, message = "Validation timeout must be at least 1000ms")
        private long validationTimeout = 5000;
        
        @Min(value = 1, message = "Initialization fail timeout must be at least 1")
        private long initializationFailTimeout = 1;
        
        @NotBlank(message = "Connection test query is required")
        private String connectionTestQuery = "SELECT 1";
        
        private boolean autoCommit = false;
    }
    
    @PostConstruct
    public void validateConfig() {
        log.info("Database configuration loaded:");
        log.info("URL: {}", maskUrl(url));
        log.info("Username: {}", username);
        log.info("Driver: {}", driverClassName);
        log.info("Max Pool Size: {}", hikari.maximumPoolSize);
        log.info("Min Idle: {}", hikari.minimumIdle);
        log.info("Connection Timeout: {}ms", hikari.connectionTimeout);
        log.info("Idle Timeout: {}ms", hikari.idleTimeout);
        log.info("Max Lifetime: {}ms", hikari.maxLifetime);
        log.info("Leak Detection Threshold: {}ms", hikari.leakDetectionThreshold);
        log.info("Auto Commit: {}", hikari.autoCommit);
        
        if (!url.startsWith("jdbc:postgresql://")) {
            log.warn("Database URL should start with 'jdbc:postgresql://' for PostgreSQL");
        }
        
        if (hikari.minimumIdle > hikari.maximumPoolSize) {
            log.warn("Minimum idle ({}) is greater than maximum pool size ({})", 
                    hikari.minimumIdle, hikari.maximumPoolSize);
        }
        
        if (hikari.maxLifetime <= hikari.idleTimeout) {
            log.warn("Max lifetime ({}) should be greater than idle timeout ({})", 
                    hikari.maxLifetime, hikari.idleTimeout);
        }
        
        log.info("Database configuration validation completed");
    }
    
    private String maskUrl(String url) {
        if (url == null) return "null";
        return url.replaceAll("://([^:]+):([^@]+)@", "://$1:****@");
    }
    
    public boolean isProductionEnvironment() {
        String env = System.getenv("ENVIRONMENT");
        return "production".equalsIgnoreCase(env) || "prod".equalsIgnoreCase(env);
    }
    
    public boolean isDevelopmentEnvironment() {
        String env = System.getenv("ENVIRONMENT");
        return "development".equalsIgnoreCase(env) || "dev".equalsIgnoreCase(env);
    }
    
    public boolean isTestEnvironment() {
        String env = System.getenv("ENVIRONMENT");
        return "test".equalsIgnoreCase(env) || "testing".equalsIgnoreCase(env);
    }
    
    // get database name from url
    public String getDatabaseName() {
        try {
            if (url != null && url.contains("/")) {
                String[] parts = url.split("/");
                String dbPart = parts[parts.length - 1];
                // remove query parameters if any
                if (dbPart.contains("?")) {
                    dbPart = dbPart.split("\\?")[0];
                }
                return dbPart;
            }
        } catch (Exception e) {
            log.warn("Could not extract database name from URL", e);
        }
        return "unknown";
    }
    
    // get host from url
    public String getDatabaseHost() {
        try {
            if (url != null && url.contains("://")) {
                String hostPart = url.split("://")[1];
                if (hostPart.contains("@")) {
                    hostPart = hostPart.split("@")[1];
                }
                if (hostPart.contains("/")) {
                    hostPart = hostPart.split("/")[0];
                }
                if (hostPart.contains(":")) {
                    hostPart = hostPart.split(":")[0];
                }
                return hostPart;
            }
        } catch (Exception e) {
            log.warn("Could not extract host from URL", e);
        }
        return "unknown";
    }
    
    // get port from url
    public int getDatabasePort() {
        try {
            if (url != null && url.contains("://")) {
                String hostPart = url.split("://")[1];
                if (hostPart.contains("@")) {
                    hostPart = hostPart.split("@")[1];
                }
                if (hostPart.contains("/")) {
                    hostPart = hostPart.split("/")[0];
                }
                if (hostPart.contains(":")) {
                    String portPart = hostPart.split(":")[1];
                    return Integer.parseInt(portPart);
                }
            }
        } catch (Exception e) {
            log.warn("Could not extract port from URL", e);
        }
        return 5432; // default postgresql port
    }
}
