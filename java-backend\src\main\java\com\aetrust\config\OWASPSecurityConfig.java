package com.aetrust.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.regex.Pattern;


@Configuration
public class OWASPSecurityConfig implements WebMvcConfigurer {
    
    @Value("${aetrust.feature-flags.xss-protection.enabled:true}")
    private boolean xssProtectionEnabled;
    
    @Value("${aetrust.feature-flags.sql-injection-protection.enabled:true}")
    private boolean sqlInjectionProtectionEnabled;
    
    @Value("${aetrust.feature-flags.input-validation.enabled:true}")
    private boolean inputValidationEnabled;
    
    @Value("${aetrust.feature-flags.secure-headers.enabled:true}")
    private boolean secureHeadersEnabled;
    
    /**
     * OWASP A01:2021 - Broken Access Control
     * OWASP A03:2021 - Injection (XSS, SQL Injection)
     * OWASP A05:2021 - Security Misconfiguration
     */
    @Bean
    public OncePerRequestFilter owaspSecurityFilter() {
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                    FilterChain filterChain) throws ServletException, IOException {
                
                // OWASP A05:2021 - Security Misconfiguration (Secure Headers)
                if (secureHeadersEnabled) {
                    addSecurityHeaders(response);
                }
                
                // OWASP A03:2021 - Injection Protection
                if (inputValidationEnabled && isVulnerableRequest(request)) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    response.getWriter().write("{\"error\":\"Invalid input detected\",\"code\":\"SECURITY_VIOLATION\"}");
                    return;
                }
                
                // OWASP A06:2021 - Vulnerable and Outdated Components (Content Type Validation)
                if (!isValidContentType(request)) {
                    response.setStatus(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE);
                    response.getWriter().write("{\"error\":\"Unsupported content type\",\"code\":\"INVALID_CONTENT_TYPE\"}");
                    return;
                }
                
                filterChain.doFilter(request, response);
            }
        };
    }
    
    /**
     * OWASP A05:2021 - Security Misconfiguration
     * Add comprehensive security headers
     */
    private void addSecurityHeaders(HttpServletResponse response) {
        // XSS Protection
        response.setHeader("X-XSS-Protection", "1; mode=block");
        
        // Content Type Options
        response.setHeader("X-Content-Type-Options", "nosniff");
        
        // Frame Options (Clickjacking Protection)
        response.setHeader("X-Frame-Options", "DENY");
        
        // Referrer Policy
        response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
        
        // Content Security Policy (XSS Protection)
        response.setHeader("Content-Security-Policy", 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "img-src 'self' data: https:; " +
            "font-src 'self' https:; " +
            "connect-src 'self' https:; " +
            "frame-ancestors 'none'; " +
            "base-uri 'self'; " +
            "form-action 'self'; " +
            "upgrade-insecure-requests");
        
        // Permissions Policy (Feature Policy)
        response.setHeader("Permissions-Policy", 
            "geolocation=(), microphone=(), camera=(), payment=(), usb=(), " +
            "accelerometer=(), gyroscope=(), magnetometer=(), fullscreen=(self)");
        
        // HSTS (HTTPS Enforcement)
        if (isSecureRequest()) {
            response.setHeader("Strict-Transport-Security", 
                "max-age=31536000; includeSubDomains; preload");
        }
        
        // Cache Control for sensitive endpoints
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }
    
    /**
     * OWASP A03:2021 - Injection Protection
     * Detect potential injection attacks
     */
    private boolean isVulnerableRequest(HttpServletRequest request) {
        if (!inputValidationEnabled) return false;
        
        // SQL Injection patterns
        Pattern[] sqlInjectionPatterns = {
            Pattern.compile("(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute).*"),
            Pattern.compile("(?i).*(script|javascript|vbscript|onload|onerror|onclick).*"),
            Pattern.compile("(?i).*(<|>|&lt;|&gt;|%3C|%3E).*"),
            Pattern.compile("(?i).*(\\||;|--|#|/\\*|\\*/).*"),
            Pattern.compile("(?i).*(char|nchar|varchar|nvarchar|ascii|substring).*\\(.*\\).*"),
            Pattern.compile("(?i).*(waitfor|delay|sleep).*"),
            Pattern.compile("(?i).*(benchmark|pg_sleep|dbms_pipe).*")
        };
        
        // XSS patterns
        Pattern[] xssPatterns = {
            Pattern.compile("(?i).*<script[^>]*>.*</script>.*"),
            Pattern.compile("(?i).*javascript:.*"),
            Pattern.compile("(?i).*on\\w+\\s*=.*"),
            Pattern.compile("(?i).*<iframe[^>]*>.*"),
            Pattern.compile("(?i).*<object[^>]*>.*"),
            Pattern.compile("(?i).*<embed[^>]*>.*"),
            Pattern.compile("(?i).*<link[^>]*>.*"),
            Pattern.compile("(?i).*<meta[^>]*>.*")
        };
        
        // Check all request parameters
        String queryString = request.getQueryString();
        if (queryString != null && containsVulnerablePattern(queryString, sqlInjectionPatterns, xssPatterns)) {
            return true;
        }
        
        // Check headers for injection attempts
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && containsVulnerablePattern(userAgent, sqlInjectionPatterns, xssPatterns)) {
            return true;
        }
        
        String referer = request.getHeader("Referer");
        if (referer != null && containsVulnerablePattern(referer, sqlInjectionPatterns, xssPatterns)) {
            return true;
        }
        
        return false;
    }
    
    private boolean containsVulnerablePattern(String input, Pattern[]... patternGroups) {
        for (Pattern[] patterns : patternGroups) {
            for (Pattern pattern : patterns) {
                if (pattern.matcher(input).matches()) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * OWASP A06:2021 - Vulnerable and Outdated Components
     * Validate content types
     */
    private boolean isValidContentType(HttpServletRequest request) {
        String contentType = request.getContentType();
        String method = request.getMethod();
        
        // Allow GET, HEAD, OPTIONS without content type
        if ("GET".equals(method) || "HEAD".equals(method) || "OPTIONS".equals(method)) {
            return true;
        }
        
        // For POST, PUT, PATCH - validate content type
        if ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method)) {
            if (contentType == null) {
                return false;
            }
            
            // Allow only specific content types
            return contentType.startsWith("application/json") ||
                   contentType.startsWith("application/x-www-form-urlencoded") ||
                   contentType.startsWith("multipart/form-data") ||
                   contentType.startsWith("text/plain");
        }
        
        return true;
    }
    
    private boolean isSecureRequest() {
        String environment = System.getenv("ENVIRONMENT");
        return "production".equalsIgnoreCase(environment);
    }
}
