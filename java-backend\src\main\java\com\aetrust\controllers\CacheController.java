package com.aetrust.controllers;

import com.aetrust.services.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@RestController
@RequestMapping("/cache")
public class CacheController {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private Environment environment;

    @Value("${spring.redis.enabled:false}")
    private boolean redisEnabled;

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

 
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getCacheInfo() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache info retrieved");
            response.put("data", cacheService.getCacheInfo());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting cache info: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to get cache info");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

  
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            boolean healthy = cacheService.isHealthy();
            Map<String, Object> response = new HashMap<>();
            response.put("success", healthy);
            response.put("message", healthy ? "Cache is healthy" : "Cache health check failed");
            response.put("timestamp", LocalDateTime.now());

            response.put("environment", environment.getProperty("ENVIRONMENT", "development"));
            response.put("redis_configured", environment.getProperty("spring.redis.host") != null);
            response.put("redis_database", environment.getProperty("spring.redis.database", "0"));

            response.put("redis_enabled", redisEnabled);
            response.put("redis_host", redisHost);
            response.put("redis_port", redisPort);

            Map<String, Object> redisConfig = new HashMap<>();
            redisConfig.put("timeout", environment.getProperty("spring.redis.timeout", "10000ms"));
            redisConfig.put("max_active", environment.getProperty("spring.redis.lettuce.pool.max-active", "10"));
            redisConfig.put("max_idle", environment.getProperty("spring.redis.lettuce.pool.max-idle", "10"));
            redisConfig.put("min_idle", environment.getProperty("spring.redis.lettuce.pool.min-idle", "1"));
            response.put("redis_config", redisConfig);

            response.put("data", Map.of("healthy", healthy));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Cache health check error: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Cache health check failed");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/config")
    public ResponseEntity<String> getCacheConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("Status", "00");
            config.put("Message", "Success");

            Map<String, Object> data = new HashMap<>();
            data.put("redis_enabled", redisEnabled);
            data.put("redis_host", redisHost);
            data.put("redis_port", redisPort);
            data.put("environment", environment.getProperty("ENVIRONMENT", "development"));
            data.put("redis_database", environment.getProperty("spring.redis.database", "0"));
            data.put("timeout", environment.getProperty("spring.redis.timeout", "10000ms"));
            data.put("timestamp", LocalDateTime.now());

            config.put("Data", data);

            // switch based status handling like gateway
            String status = (String) config.get("Status");
            String responseJson = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(config);

            return switch (status) {
                case "00", "000", "200", "Success" -> ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(responseJson);
                case "001", "010", "401" -> ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.TEXT_PLAIN)
                        .body(responseJson);
                default -> ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .contentType(MediaType.TEXT_PLAIN)
                        .body(responseJson);
            };

        } catch (Exception ex) {
            log.error("Error getting cache config: {}", ex.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Bad Request");
        }
    }


    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testCache(@RequestBody Map<String, String> request) {
        try {
            String key = request.getOrDefault("key", "test_key");
            String value = request.getOrDefault("value", "test_value");
            int ttl = Integer.parseInt(request.getOrDefault("ttl", "60"));

            cacheService.set(key, value, ttl, TimeUnit.SECONDS);
            String retrieved = cacheService.get(key);
            boolean exists = cacheService.exists(key);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache test completed");
            response.put("data", Map.of(
                "key", key,
                "value_set", value,
                "value_retrieved", retrieved,
                "exists", exists,
                "test_passed", value.equals(retrieved)
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Cache test error: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Cache test failed");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

 
    @DeleteMapping("/clear")
    public ResponseEntity<Map<String, Object>> clearCache() {
        try {
            cacheService.clear();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache cleared successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Cache clear error: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to clear cache");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

 
    @GetMapping("/get/{key}")
    public ResponseEntity<Map<String, Object>> getCacheValue(@PathVariable String key) {
        try {
            String value = cacheService.get(key);
            boolean exists = cacheService.exists(key);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", exists ? "Cache value found" : "Cache key not found");
            response.put("data", Map.of(
                "key", key,
                "value", value,
                "exists", exists
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting cache value for key {}: {}", key, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to get cache value");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

  
    @PostMapping("/set")
    public ResponseEntity<Map<String, Object>> setCacheValue(@RequestBody Map<String, Object> request) {
        try {
            String key = (String) request.get("key");
            String value = (String) request.get("value");
            Integer ttl = (Integer) request.getOrDefault("ttl", 3600);

            if (key == null || value == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Key and value are required");
                return ResponseEntity.badRequest().body(response);
            }

            cacheService.set(key, value, ttl, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache value set successfully");
            response.put("data", Map.of(
                "key", key,
                "value", value,
                "ttl_seconds", ttl
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error setting cache value: {}", e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to set cache value");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

   
    @DeleteMapping("/delete/{key}")
    public ResponseEntity<Map<String, Object>> deleteCacheKey(@PathVariable String key) {
        try {
            boolean existed = cacheService.exists(key);
            cacheService.delete(key);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", existed ? "Cache key deleted" : "Cache key did not exist");
            response.put("data", Map.of(
                "key", key,
                "existed", existed
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting cache key {}: {}", key, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete cache key");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }
}
