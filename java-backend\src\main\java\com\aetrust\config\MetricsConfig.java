package com.aetrust.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Configuration
@EnableScheduling
public class MetricsConfig {

    @Autowired
    private MeterRegistry meterRegistry;

    // Custom metrics
    private final AtomicLong activeUsers = new AtomicLong(0);
    private final AtomicLong totalTransactions = new AtomicLong(0);
    private final AtomicLong failedLogins = new AtomicLong(0);
    private final AtomicLong rateLimitExceeded = new AtomicLong(0);
    private final AtomicLong securityViolations = new AtomicLong(0);

    @Bean
    public Counter loginSuccessCounter() {
        return Counter.builder("aetrust.auth.login.success")
            .description("Number of successful logins")
            .tag("type", "authentication")
            .register(meterRegistry);
    }

    @Bean
    public Counter loginFailureCounter() {
        return Counter.builder("aetrust.auth.login.failure")
            .description("Number of failed login attempts")
            .tag("type", "authentication")
            .register(meterRegistry);
    }

    @Bean
    public Counter registrationCounter() {
        return Counter.builder("aetrust.auth.registration")
            .description("Number of user registrations")
            .tag("type", "authentication")
            .register(meterRegistry);
    }

    @Bean
    public Counter transactionCounter() {
        return Counter.builder("aetrust.transaction.total")
            .description("Total number of transactions")
            .tag("type", "business")
            .register(meterRegistry);
    }

    @Bean
    public Counter rateLimitCounter() {
        return Counter.builder("aetrust.security.rate_limit_exceeded")
            .description("Number of rate limit violations")
            .tag("type", "security")
            .register(meterRegistry);
    }

    @Bean
    public Counter securityViolationCounter() {
        return Counter.builder("aetrust.security.violations")
            .description("Number of security violations detected")
            .tag("type", "security")
            .register(meterRegistry);
    }

    @Bean
    public Timer requestTimer() {
        return Timer.builder("aetrust.http.requests")
            .description("HTTP request duration")
            .tag("type", "http")
            .register(meterRegistry);
    }

    @Bean
    public Timer databaseQueryTimer() {
        return Timer.builder("aetrust.database.query.duration")
            .description("Database query execution time")
            .tag("type", "database")
            .register(meterRegistry);
    }

    @Bean
    public Gauge activeUsersGauge() {
        return Gauge.builder("aetrust.users.active", activeUsers, AtomicLong::get)
            .description("Number of currently active users")
            .tag("type", "business")
            .register(meterRegistry);
    }

    @Bean
    public Gauge memoryUsageGauge() {
        return Gauge.builder("aetrust.system.memory.usage", this, obj -> obj.getMemoryUsagePercent())
            .description("Current memory usage percentage")
            .tag("type", "system")
            .register(meterRegistry);
    }

    @Bean
    public Gauge databaseConnectionsGauge() {
        return Gauge.builder("aetrust.database.connections.active", this, obj -> obj.getActiveDatabaseConnections())
            .description("Number of active database connections")
            .tag("type", "database")
            .register(meterRegistry);
    }

    // Scheduled metrics collection
    @Scheduled(fixedRate = 60000) // every minute
    public void collectSystemMetrics() {
        try {
            // collect JVM metrics
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            meterRegistry.gauge("aetrust.system.memory.total", totalMemory);
            meterRegistry.gauge("aetrust.system.memory.used", usedMemory);
            meterRegistry.gauge("aetrust.system.memory.free", freeMemory);

            // collect thread metrics
            ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
            while (rootGroup.getParent() != null) {
                rootGroup = rootGroup.getParent();
            }
            meterRegistry.gauge("aetrust.system.threads.active", rootGroup.activeCount());

        } catch (Exception e) {
            log.error("Error collecting system metrics: {}", e.getMessage());
        }
    }

    @Scheduled(fixedRate = 300000) // every 5 minutes
    public void collectBusinessMetrics() {
        try {
            // collect business-specific metrics
            // this would normally query the database for real metrics
            
            log.debug("Collecting business metrics - Active users: {}, Total transactions: {}", 
                     activeUsers.get(), totalTransactions.get());

        } catch (Exception e) {
            log.error("Error collecting business metrics: {}", e.getMessage());
        }
    }

    // Helper methods for gauge values
    private double getMemoryUsagePercent() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        return (double) usedMemory / totalMemory * 100;
    }

    private double getActiveDatabaseConnections() {
        // this would normally get actual connection pool metrics
        // for now return a placeholder value
        return 5.0;
    }

    // Methods to update custom metrics
    public void incrementActiveUsers() {
        activeUsers.incrementAndGet();
    }

    public void decrementActiveUsers() {
        activeUsers.decrementAndGet();
    }

    public void incrementTotalTransactions() {
        totalTransactions.incrementAndGet();
    }

    public void incrementFailedLogins() {
        failedLogins.incrementAndGet();
    }

    public void incrementRateLimitExceeded() {
        rateLimitExceeded.incrementAndGet();
    }

    public void incrementSecurityViolations() {
        securityViolations.incrementAndGet();
    }

    // Getters for current values
    public long getActiveUsersCount() {
        return activeUsers.get();
    }

    public long getTotalTransactionsCount() {
        return totalTransactions.get();
    }

    public long getFailedLoginsCount() {
        return failedLogins.get();
    }

    public long getRateLimitExceededCount() {
        return rateLimitExceeded.get();
    }

    public long getSecurityViolationsCount() {
        return securityViolations.get();
    }
}
