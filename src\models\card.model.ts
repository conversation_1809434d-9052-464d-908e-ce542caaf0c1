import mongoose, { Schema, Document } from 'mongoose';
import { Currency } from '../types';

export enum CardType {
  VIRTUAL = 'virtual',
  PHYSICAL = 'physical'
}

export enum CardStatus {
  ACTIVE = 'active',
  BLOCKED = 'blocked',
  EXPIRED = 'expired',
  PENDING = 'pending',
  CANCELLED = 'cancelled'
}

export enum CardBrand {
  VISA = 'visa',
  MASTERCARD = 'mastercard',
  VERVE = 'verve'
}

export interface ICard extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  wallet_id: mongoose.Types.ObjectId;
  card_type: CardType;
  card_brand: CardBrand;
  card_number: string; // encrypted
  masked_number: string; // masked card
  card_holder_name: string;
  expiry_month: number;
  expiry_year: number;
  expiry_date: string; // formatted expiry date MM/YY
  is_expired: boolean;
  cvv: string; // encrypted
  pin: string; // encrypted
  status: CardStatus;
  currency: Currency;
  limits: {
    daily_spend_limit: number;
    monthly_spend_limit: number;
    atm_withdrawal_limit: number;
    online_transaction_limit: number;
    pos_transaction_limit: number;
  };
  settings: {
    international_transactions: boolean;
    online_transactions: boolean;
    atm_withdrawals: boolean;
    pos_transactions: boolean;
    contactless_payments: boolean;
    balance_visibility: boolean;
    auto_lock_enabled: boolean;
  };
  ui_preferences: {
    card_theme: string;
    show_balance: boolean;
    show_card_number: boolean;
    notification_preferences: {
      transaction_alerts: boolean;
      spending_limit_alerts: boolean;
      security_alerts: boolean;
    };
  };
  tier_info: {
    tier_level: 'basic' | 'standard' | 'premium' | 'platinum';
    tier_benefits: string[];
    upgrade_eligible: boolean;
    next_tier?: string;
  };
  security: {
    is_locked: boolean;
    lock_reason?: string;
    failed_pin_attempts: number;
    last_failed_attempt?: Date;
    locked_until?: Date;
  };
  usage_stats: {
    total_transactions: number;
    total_spent: number;
    last_transaction_date?: Date;
    last_used_location?: string;
  };
  delivery_info?: {
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postal_code: string;
    };
    delivery_status: 'pending' | 'shipped' | 'delivered' | 'failed';
    tracking_number?: string;
    delivery_date?: Date;
  };
  created_at: Date;
  updated_at: Date;

  toggleBalanceVisibility(): Promise<void>;
  getDisplayData(): any;
  getTierBenefits(): any;
  getCopyableData(): any;
  updateCardTheme(theme: string): Promise<void>;
}

const cardSchema = new Schema<ICard>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  wallet_id: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    required: true
  },
  card_type: {
    type: String,
    enum: Object.values(CardType),
    required: true
  },
  card_brand: {
    type: String,
    enum: Object.values(CardBrand),
    required: true
  },
  card_number: {
    type: String,
    required: true,
    unique: true
  },
  masked_number: {
    type: String,
    required: true
  },
  card_holder_name: {
    type: String,
    required: true,
    uppercase: true
  },
  expiry_month: {
    type: Number,
    required: true,
    min: 1,
    max: 12
  },
  expiry_year: {
    type: Number,
    required: true,
    min: new Date().getFullYear()
  },
  expiry_date: {
    type: String,
    required: true
  },
  is_expired: {
    type: Boolean,
    default: false
  },
  cvv: {
    type: String,
    required: true
  },
  pin: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: Object.values(CardStatus),
    default: CardStatus.PENDING
  },
  currency: {
    type: String,
    enum: Object.values(Currency),
    required: true
  },
  limits: {
    daily_spend_limit: { type: Number, default: 50000 },
    monthly_spend_limit: { type: Number, default: 500000 },
    atm_withdrawal_limit: { type: Number, default: 20000 },
    online_transaction_limit: { type: Number, default: 100000 },
    pos_transaction_limit: { type: Number, default: 50000 }
  },
  settings: {
    international_transactions: { type: Boolean, default: false },
    online_transactions: { type: Boolean, default: true },
    atm_withdrawals: { type: Boolean, default: true },
    pos_transactions: { type: Boolean, default: true },
    contactless_payments: { type: Boolean, default: true },
    balance_visibility: { type: Boolean, default: true },
    auto_lock_enabled: { type: Boolean, default: false }
  },
  ui_preferences: {
    card_theme: { type: String, default: 'default' },
    show_balance: { type: Boolean, default: true },
    show_card_number: { type: Boolean, default: false },
    notification_preferences: {
      transaction_alerts: { type: Boolean, default: true },
      spending_limit_alerts: { type: Boolean, default: true },
      security_alerts: { type: Boolean, default: true }
    }
  },
  tier_info: {
    tier_level: {
      type: String,
      enum: ['basic', 'standard', 'premium', 'platinum'],
      default: 'basic'
    },
    tier_benefits: [{ type: String }],
    upgrade_eligible: { type: Boolean, default: true },
    next_tier: { type: String }
  },
  security: {
    is_locked: { type: Boolean, default: false },
    lock_reason: { type: String, default: '' },
    failed_pin_attempts: { type: Number, default: 0 },
    last_failed_attempt: { type: Date },
    locked_until: { type: Date }
  },
  usage_stats: {
    total_transactions: { type: Number, default: 0 },
    total_spent: { type: Number, default: 0 },
    last_transaction_date: { type: Date },
    last_used_location: { type: String, default: '' }
  },
  delivery_info: {
    address: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      country: { type: String },
      postal_code: { type: String }
    },
    delivery_status: {
      type: String,
      enum: ['pending', 'shipped', 'delivered', 'failed'],
      default: 'pending'
    },
    tracking_number: { type: String },
    delivery_date: { type: Date }
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Individual indexes
cardSchema.index({ user_id: 1 });
cardSchema.index({ wallet_id: 1 });
cardSchema.index({ card_type: 1 });
cardSchema.index({ status: 1 });
cardSchema.index({ created_at: -1 });

// Compound indexes
cardSchema.index({ user_id: 1, status: 1 });


cardSchema.virtual('masked_card_number').get(function() {
  if (!this.card_number) return '';
  try {
    const { CryptoUtils } = require('../utils/crypto');
    const decrypted = CryptoUtils.decrypt(this.card_number);
    return `****-****-****-${decrypted.slice(-4)}`;
  } catch (error) {
    return '****-****-****-****';
  }
});

cardSchema.methods.canTransact = function(): boolean {
  return this.status === CardStatus.ACTIVE && 
         !this.security.is_locked && 
         !this.is_expired;
};

cardSchema.methods.isWithinLimit = function(amount: number, limitType: string): boolean {
  const limits = this.limits;
  switch (limitType) {
    case 'daily': return amount <= limits.daily_spend_limit;
    case 'monthly': return amount <= limits.monthly_spend_limit;
    case 'atm': return amount <= limits.atm_withdrawal_limit;
    case 'online': return amount <= limits.online_transaction_limit;
    case 'pos': return amount <= limits.pos_transaction_limit;
    default: return false;
  }
};

cardSchema.statics.findActiveByUser = function(userId: string) {
  return this.find({
    user_id: userId,
    status: { $in: [CardStatus.ACTIVE, CardStatus.BLOCKED] }
  }).sort({ created_at: -1 });
};

cardSchema.statics.findByCardNumber = function(cardNumber: string) {
  return this.findOne({ card_number: cardNumber });
};

cardSchema.methods.toggleBalanceVisibility = function(): Promise<ICard> {
  this.ui_preferences.show_balance = !this.ui_preferences.show_balance;
  return this.save();
};

cardSchema.methods.updateCardTheme = function(theme: string): Promise<ICard> {
  this.ui_preferences.card_theme = theme;
  return this.save();
};

cardSchema.methods.getDisplayData = function() {
  return {
    id: this._id.toString(),
    cardNumber: this.ui_preferences.show_card_number ? this.masked_number : '****-****-****-****',
    cardHolder: this.card_holder_name,
    expiryDate: this.expiry_date,
    cardType: this.card_type,
    cardBrand: this.card_brand,
    status: this.status,
    theme: this.ui_preferences.card_theme,
    balanceVisible: this.ui_preferences.show_balance,
    tier: this.tier_info.tier_level,
    canTransact: this.canTransact(),
    isExpired: this.is_expired
  };
};

cardSchema.methods.getTierBenefits = function() {
  const tierBenefits = {
    basic: [
      'Basic transaction limits',
      'Standard customer support',
      'Mobile app access'
    ],
    standard: [
      'Increased transaction limits',
      'Priority customer support',
      'Mobile app access',
      'Online banking'
    ],
    premium: [
      'High transaction limits',
      'Premium customer support',
      'Mobile app access',
      'Online banking',
      'International transactions',
      'Cashback rewards'
    ],
    platinum: [
      'Unlimited transaction limits',
      'VIP customer support',
      'Mobile app access',
      'Online banking',
      'International transactions',
      'Premium cashback rewards',
      'Airport lounge access',
      'Concierge services'
    ]
  };

  return {
    currentTier: this.tier_info.tier_level,
    benefits: tierBenefits[this.tier_info.tier_level as keyof typeof tierBenefits] || tierBenefits.basic,
    upgradeEligible: this.tier_info.upgrade_eligible,
    nextTier: this.tier_info.next_tier
  };
};

cardSchema.methods.getCopyableData = function() {
  try {
    const { CryptoUtils } = require('../utils/crypto');
    return {
      cardNumber: CryptoUtils.decrypt(this.card_number),
      expiryDate: this.expiry_date,
      cvv: CryptoUtils.decrypt(this.cvv),
      cardHolder: this.card_holder_name
    };
  } catch (error) {
    return {
      cardNumber: '****-****-****-****',
      expiryDate: this.expiry_date,
      cvv: '***',
      cardHolder: this.card_holder_name
    };
  }
};

cardSchema.methods.toggleBalanceVisibility = function() {
  this.ui_preferences.show_balance = !this.ui_preferences.show_balance;
  return this.save();
};

cardSchema.methods.updateCardTheme = function(theme: string) {
  this.ui_preferences.card_theme = theme;
  return this.save();
};

cardSchema.pre('save', function(next) {
  this.expiry_date = `${this.expiry_month.toString().padStart(2, '0')}/${this.expiry_year.toString().slice(-2)}`;
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  this.is_expired = (this.expiry_year < currentYear) || (this.expiry_year === currentYear && this.expiry_month < currentMonth);

  if (this.isModified('card_number') && this.card_number) {
    const cardNum = this.card_number.replace(/\D/g, '');
    this.masked_number = `****-****-****-${cardNum.slice(-4)}`;
  }

  next();
});

export const Card = mongoose.model<ICard>('Card', cardSchema);
