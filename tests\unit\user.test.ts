import { UserService } from '../../src/services/user.service';
import { User } from '../../src/models/user.model';
import { createTestUser, setupTestDb, teardownTestDb, clearDatabase } from '../setup';

describe('UserService', () => {
  beforeAll(async () => {
    await setupTestDb();
  });

  afterAll(async () => {
    await teardownTestDb();
  });

  beforeEach(async () => {
    await clearDatabase();
  });

  describe('getUserById', () => {
    it('should return user by valid ID', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const result = await UserService.getUserById(user._id.toString());

      expect(result).toBeTruthy();
      expect(result?.email).toBe(userData.email);
      expect(result?.first_name).toBe(userData.first_name);
    });

    it('should return null for invalid ID', async () => {
      const result = await UserService.getUserById('507f1f77bcf86cd799439011');

      expect(result).toBeNull();
    });

    it('should return null for malformed ID', async () => {
      const result = await UserService.getUserById('invalid-id');

      expect(result).toBeNull();
    });
  });

  describe('getUserByEmail', () => {
    it('should return user by valid email', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const result = await UserService.getUserByEmail(userData.email);

      expect(result).toBeTruthy();
      expect(result?.email).toBe(userData.email);
    });

    it('should return null for non-existent email', async () => {
      const result = await UserService.getUserByEmail('<EMAIL>');

      expect(result).toBeNull();
    });
  });

  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        bio: 'Updated bio'
      };

      const result = await UserService.updateProfile(user._id.toString(), updateData);

      if (!result) {
        throw new Error('Update profile returned null');
      }

      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data.first_name).toBe('Updated');
        expect(result.data.last_name).toBe('Name');
        expect(result.data.bio).toBe('Updated bio');
      }
    });

    it('should fail to update non-existent user', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name'
      };

      const result = await UserService.updateProfile('507f1f77bcf86cd799439011', updateData);

      if (!result) {
        throw new Error('Update profile returned null');
      }

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.message).toContain('user not found');
      }
    });

    it('should not update restricted fields', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const updateData = {
        firstName: 'Updated'
      };

      const result = await UserService.updateProfile(user._id.toString(), updateData);

      if (!result) {
        throw new Error('Update profile returned null');
      }

      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data.email).toBe(userData.email); // Should remain unchanged
        expect(result.data.role).toBe(userData.role); // Should remain unchanged
        expect(result.data.first_name).toBe('Updated'); // Should be updated
      }
    });
  });

  describe('deleteAccount', () => {
    it('should soft delete user account', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const result = await UserService.deleteAccount(user._id.toString());

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.message).toContain('account deleted successfully');
      }

      // Verify user is soft deleted
      const deletedUser = await User.findById(user._id);
      expect(deletedUser?.deleted_at).toBeTruthy();
    });

    it('should fail to delete non-existent user', async () => {
      const result = await UserService.deleteAccount('507f1f77bcf86cd799439011');

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.message).toContain('user not found');
      }
    });
  });

  describe('searchUsers', () => {
    beforeEach(async () => {
      // Create test users
      const users = [
        createTestUser({ email: '<EMAIL>', first_name: 'John', last_name: 'Doe' }),
        createTestUser({ email: '<EMAIL>', first_name: 'Jane', last_name: 'Smith' }),
        createTestUser({ email: '<EMAIL>', first_name: 'Bob', last_name: 'Johnson' })
      ];

      for (const userData of users) {
        const user = new User(userData);
        await user.save();
      }
    });

    it('should search users by name', async () => {
      const result = await UserService.searchUsers({ search: 'Jane' });

      expect(result.users).toHaveLength(1);
      if (result.users.length > 0) {
        expect(result.users[0]?.first_name).toBe('Jane');
      }
    });

    it('should search users by email', async () => {
      const result = await UserService.searchUsers({ search: '<EMAIL>' });

      expect(result.users).toHaveLength(1);
      if (result.users.length > 0) {
        expect(result.users[0]?.email).toBe('<EMAIL>');
      }
    });

    it('should return empty results for no matches', async () => {
      const result = await UserService.searchUsers({ search: 'nonexistent' });

      expect(result.users).toHaveLength(0);
    });

    it('should paginate search results', async () => {
      const result = await UserService.searchUsers({ page: 1, limit: 2 });

      expect(result.users).toHaveLength(2);
      expect(result.page).toBe(1);
      expect(result.total).toBe(3);
    });
  });

  describe('updateUserStatus', () => {
    it('should update user account status', async () => {
      const userData = createTestUser({ account_status: 'active' });
      const user = new User(userData);
      await user.save();

      const result = await UserService.updateUserStatus(user._id.toString(), 'suspended' as any);

      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data.account_status).toBe('suspended');
      }
    });

    it('should fail to update status for non-existent user', async () => {
      const result = await UserService.updateUserStatus('507f1f77bcf86cd799439011', 'suspended' as any);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.message).toContain('user not found');
      }
    });
  });

  describe('getUserStats', () => {
    it('should return user statistics', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      await user.save();

      const result = await UserService.getUserStats(user._id.toString());

      expect(result.success).toBe(true);
      if (result.success && result.data) {
        expect(result.data).toHaveProperty('totalTransactions');
        expect(result.data).toHaveProperty('totalVolume');
        expect(result.data).toHaveProperty('walletBalance');
        expect(result.data).toHaveProperty('accountAge');
      }
    });

    it('should fail to get stats for non-existent user', async () => {
      const result = await UserService.getUserStats('507f1f77bcf86cd799439011');

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.message).toContain('user not found');
      }
    });
  });
});
