package com.aetrust.repositories;

import com.aetrust.models.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserPreferencesRepository extends JpaRepository<User.UserPreferences, Long> {

    Optional<User.UserPreferences> findByUserIdAndDeletedAtIsNull(Long userId);
    Optional<User.UserPreferences> findByUserUuidAndDeletedAtIsNull(UUID userUuid);

    boolean existsByUserIdAndDeletedAtIsNull(Long userId);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    List<User.UserPreferences> findByLanguageAndDeletedAtIsNull(String language);
    List<User.UserPreferences> findByTimezoneAndDeletedAtIsNull(String timezone);
    List<User.UserPreferences> findByCurrencyAndDeletedAtIsNull(String currency);
    List<User.UserPreferences> findByThemeAndDeletedAtIsNull(String theme);

    List<User.UserPreferences> findByEmailNotificationsAndDeletedAtIsNull(boolean enabled);
    List<User.UserPreferences> findBySmsNotificationsAndDeletedAtIsNull(boolean enabled);
    List<User.UserPreferences> findByPushNotificationsAndDeletedAtIsNull(boolean enabled);

    @Query("SELECT COUNT(p) FROM User$UserPreferences p WHERE p.emailNotifications = true AND p.deletedAt IS NULL")
    long countUsersWithEmailNotificationsEnabled();

    @Query("SELECT COUNT(p) FROM User$UserPreferences p WHERE p.smsNotifications = true AND p.deletedAt IS NULL")
    long countUsersWithSmsNotificationsEnabled();

    @Query("SELECT COUNT(p) FROM User$UserPreferences p WHERE p.pushNotifications = true AND p.deletedAt IS NULL")
    long countUsersWithPushNotificationsEnabled();

    @Query("SELECT p.language, COUNT(p) FROM User$UserPreferences p WHERE p.deletedAt IS NULL GROUP BY p.language")
    List<Object[]> getLanguageDistribution();

    @Query("SELECT p.currency, COUNT(p) FROM User$UserPreferences p WHERE p.deletedAt IS NULL GROUP BY p.currency")
    List<Object[]> getCurrencyDistribution();

    @Modifying
    @Query("UPDATE User$UserPreferences p SET p.deletedAt = :deletedAt WHERE p.userId = :userId")
    void softDeleteByUserId(@Param("userId") Long userId, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User$UserPreferences p SET p.deletedAt = :deletedAt WHERE p.userUuid = :userUuid")
    void softDeleteByUserUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
}
